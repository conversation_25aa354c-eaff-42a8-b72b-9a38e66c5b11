import logging
import traceback
import os
import glob
from datetime import datetime

def setup_logger():
    """
    Sets up a logger that writes to both a file and console
    Returns a configured logger instance
    Keeps only the most recent 100 log files
    """
    # Create logs directory if it doesn't exist
    base_dir = os.path.dirname(os.path.realpath(__file__))
    logs_dir = os.path.join(base_dir, 'logs')
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
    
    # Rotate logs - keep only the most recent 100 files
    rotate_logs(logs_dir, max_logs=100)
    
    # Create a unique log filename with timestamp
    log_filename = os.path.join(logs_dir, f'docusign_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    # Configure the logger
    logger = logging.getLogger('docusign_logger')
    logger.setLevel(logging.DEBUG)
    
    # Reset handlers if logger already exists
    if logger.handlers:
        logger.handlers = []
    
    # Create file handler
    file_handler = logging.FileHandler(log_filename)
    file_handler.setLevel(logging.DEBUG)
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Create formatter and add it to the handlers
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Add the handlers to the logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def rotate_logs(logs_dir, max_logs=100):
    """
    Keeps only the most recent 'max_logs' number of log files
    and deletes older ones
    """
    try:
        # Get all log files
        log_files = glob.glob(os.path.join(logs_dir, "docusign_*.log"))
        
        # If we have more logs than our limit
        if len(log_files) >= max_logs:
            # Sort files by modification time (oldest first)
            log_files.sort(key=lambda x: os.path.getmtime(x))
            
            # Remove oldest files to keep only max_logs
            files_to_delete = log_files[:-max_logs]
            for old_file in files_to_delete:
                try:
                    os.remove(old_file)
                    print(f"Removed old log file: {old_file}")
                except Exception as e:
                    print(f"Error removing log file {old_file}: {str(e)}")
    except Exception as e:
        print(f"Error during log rotation: {str(e)}")
        # Continue even if log rotation fails

def log_exception(logger, exception, context=""):
    """
    Logs detailed exception information including traceback
    """
    # Get the exception details
    exc_type = type(exception).__name__
    exc_msg = str(exception)
    exc_traceback = traceback.format_exc()
    
    # Log structured information
    logger.error(f"{'='*50}")
    logger.error(f"EXCEPTION OCCURRED: {context}")
    logger.error(f"Type: {exc_type}")
    logger.error(f"Message: {exc_msg}")
    logger.error(f"Traceback:")
    logger.error(f"{exc_traceback}")
    logger.error(f"{'='*50}")
    
    return f"{exc_type}: {exc_msg}"