2025-05-01 14:56:51,915 - docusign_logger - INFO - DocuSign process started
2025-05-01 14:56:52,467 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-05-01 14:56:53,821 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-05-01 14:56:56,930 - docusign_logger - INFO - Bulk send will send signature request to 74 employees.
2025-05-01 14:57:03,003 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2760}
2025-05-01 14:57:03,979 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwCAuV-58ojdSAgAgCEkG_uI3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.kqrHR_0cKBpeNH_t07Gi_YEIVpKIVkkeXyMFSIKygUnrk_kwhM9g6FBSotGMn2Z7gJd0bnHkZ9Ldf2kVqLH45P9azJISyKuyYPybGptNWru_iuA6-kL6prS-3pLVi6GWGvzD-P1yqk6IK8G5B7j-zDoh3DY75W3hpWrSqL4ulHLi8hLTsAgBYislnwIhz6Qy9cAevmQ5EopD-5-uz_uaKHhk9bkZpbSuN_g8a51rS_z4QwN7DfKuDfUH0ANca5iH2Wn1v5KPs2kAlZQyAs0fxEStbwtS65_DoiH8XmQ2d3SVvDhEyS_OELf3F89bCNkMrCvA5dDEieR7B8JGk6xUcQ
2025-05-01 14:57:03,980 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-05-01 14:57:16,729 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.pdf', 'doc_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.doc', 'success': True, 'error': None}
2025-05-01 14:57:16,732 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-05-01 14:57:21,330 - docusign_logger - ERROR - ==================================================
2025-05-01 14:57:21,330 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-05-01 14:57:21,330 - docusign_logger - ERROR - Type: ApiException
2025-05-01 14:57:21,330 - docusign_logger - ERROR - Message: (400)
Reason: Bad Request
Trace-Token: a8cc1dc0-1949-4a4b-9248-9388b0be73d0
Timestamp: Thu, 01 May 2025 20:57:21 GMT
HTTP response headers: HTTPHeaderDict({'Cache-Control': 'no-cache', 'Content-Length': '299', 'Content-Type': 'application/json; charset=utf-8', 'Vary': 'Origin', 'X-Content-Type-Options': 'nosniff', 'X-RateLimit-Reset': '1746133200', 'X-RateLimit-Remaining': '2996', 'X-RateLimit-Limit': '3000', 'X-BurstLimit-Remaining': '496', 'X-BurstLimit-Limit': '500', 'X-DocuSign-TraceToken': 'a8cc1dc0-1949-4a4b-9248-9388b0be73d0', 'X-DocuSign-Node': 'DA103FE120', 'Date': 'Thu, 01 May 2025 20:57:21 GMT', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'})
HTTP response body: b'{"envelopeOrTemplateId":"0dbf61a0-ccef-4edd-9c69-816b95f54edf","batchId":"00000000-0000-0000-0000-000000000000","batchSize":"1","totalQueued":"0","queueLimit":"2000","errors":["BULK_SEND_ENVELOPE_HAS_NO_TABS"],"errorDetails":["Bulk sending copy contains tabs, but the specified envelope does not."]}'

2025-05-01 14:57:21,331 - docusign_logger - ERROR - Traceback:
2025-05-01 14:57:21,331 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 525, in start_docusign_bulk_send_within_tkinter
    batch_id = bulk_sender.send_bulk_documents(final_recipients)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\docusign_bulksend.py", line 281, in send_bulk_documents
    batch = bulk_envelopes_api.create_bulk_send_request(
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\site-packages\docusign_esign\apis\bulk_envelopes_api.py", line 175, in create_bulk_send_request
    (data) = self.create_bulk_send_request_with_http_info(account_id, bulk_send_list_id, **kwargs)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\site-packages\docusign_esign\apis\bulk_envelopes_api.py", line 249, in create_bulk_send_request_with_http_info
    return self.api_client.call_api(resource_path, 'POST',
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\site-packages\docusign_esign\client\api_client.py", line 355, in call_api
    return self.__call_api(resource_path, method,
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\site-packages\docusign_esign\client\api_client.py", line 169, in __call_api
    response_data = self.request(method, url,
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\site-packages\docusign_esign\client\api_client.py", line 398, in request
    return self.rest_client.POST(url,
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\site-packages\docusign_esign\client\api_response.py", line 288, in POST
    return self.request("POST", url,
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\site-packages\docusign_esign\client\api_response.py", line 247, in request
    raise ApiException(http_resp=r)
docusign_esign.client.api_exception.ApiException: (400)
Reason: Bad Request
Trace-Token: a8cc1dc0-1949-4a4b-9248-9388b0be73d0
Timestamp: Thu, 01 May 2025 20:57:21 GMT
HTTP response headers: HTTPHeaderDict({'Cache-Control': 'no-cache', 'Content-Length': '299', 'Content-Type': 'application/json; charset=utf-8', 'Vary': 'Origin', 'X-Content-Type-Options': 'nosniff', 'X-RateLimit-Reset': '1746133200', 'X-RateLimit-Remaining': '2996', 'X-RateLimit-Limit': '3000', 'X-BurstLimit-Remaining': '496', 'X-BurstLimit-Limit': '500', 'X-DocuSign-TraceToken': 'a8cc1dc0-1949-4a4b-9248-9388b0be73d0', 'X-DocuSign-Node': 'DA103FE120', 'Date': 'Thu, 01 May 2025 20:57:21 GMT', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'})
HTTP response body: b'{"envelopeOrTemplateId":"0dbf61a0-ccef-4edd-9c69-816b95f54edf","batchId":"00000000-0000-0000-0000-000000000000","batchSize":"1","totalQueued":"0","queueLimit":"2000","errors":["BULK_SEND_ENVELOPE_HAS_NO_TABS"],"errorDetails":["Bulk sending copy contains tabs, but the specified envelope does not."]}'


2025-05-01 14:57:21,332 - docusign_logger - ERROR - ==================================================
2025-05-01 14:57:21,333 - docusign_logger - INFO - Exception occurred: <class 'docusign_esign.client.api_exception.ApiException'>, eSignatureapi_docusign_bulksend.py, 525
