2025-04-25 02:56:39,680 - docusign_logger - INFO - DocuSign process started
2025-04-25 02:56:39,861 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 1, Job = 25069., Username = olsen.sql.p4m
2025-04-25 02:56:40,814 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-25 02:56:42,801 - docusign_logger - INFO - BulkSend will send signature request to employee ranging from 1 to 1500
2025-04-25 02:56:43,365 - docusign_logger - INFO - Bulk send will send signature request to 4 employees.
2025-04-25 02:56:48,712 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {1187}
2025-04-25 02:56:49,476 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwCAHlgd14PdSAgAgIYcf9-D3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.M7pWMP14AW8hlDQlHZ87ziwuOgkXMcVQ26f6UZCoOCEscA-k63fgTiVY8o1a8I4GA8Eyoi9G7Bid3zAXqA0W2gj1rBSusl0V3SLo3XMjH2zrUQyjALD_4UzfPxnNqqvNyIx7LfxcqZ1kxL4QVhntMv1GBcKeqwsQhgUuQvd3RCYEN6-Utk9KmRkogDyDRSGYD2QGpotpPd_JkUE7xiWVtXwE0D3Y2y03mVcBHTS6M_w2UznvupdMo7cqh6-3QEfPhDwZI66nvby7VNJPB-RxIMPWJcLjUjYDjvovOwLzCyPTKUqITjdSVR6E54vozhVfN8Q3EDpkGDg4HDNOfkjl4A
2025-04-25 02:56:49,481 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-25 02:57:01,125 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.pdf', 'doc_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.doc', 'success': True, 'error': None}
2025-04-25 02:57:01,135 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-25 02:57:06,583 - docusign_logger - INFO - Newly Created Batch status: {'action': 'Send',
 'action_status': 'Processing',
 'batch_id': 'b7806cd9-86ff-4962-8503-45d45dd5d287',
 'batch_name': 'Bulk Send List for Job-25069',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': 'fa866db6-3211-4a86-b850-e4743127c726',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': None,
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/b7806cd9-86ff-4962-8503-45d45dd5d287/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '9fd6c738-73bf-44b4-a95a-83513bf9442e',
 'mailing_list_name': 'Bulk Send List for Job-25069',
 'owner_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'queued': '1',
 'resends_remaining': 'N/A',
 'sender_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'sent': '0',
 'submitted_date': '2025-04-25T15:57:05.7970000Z'} Batch ID: b7806cd9-86ff-4962-8503-45d45dd5d287
2025-04-25 02:57:06,586 - docusign_logger - INFO - Bulk send initiated with batch ID: b7806cd9-86ff-4962-8503-45d45dd5d287
2025-04-25 02:57:06,589 - docusign_logger - INFO - Bulk send initiated successfully with Batch ID: b7806cd9-86ff-4962-8503-45d45dd5d287. Cleaning up the temporary files.
2025-04-25 02:57:06,604 - docusign_logger - INFO - Background process started to monitor batch and set reminders. Please wait.
2025-04-25 02:57:16,606 - docusign_logger - INFO - Checking for envelopes in batch (Attempt 1/80)...
2025-04-25 02:57:17,644 - docusign_logger - INFO - Batch status: {'action': 'Send',
 'action_status': 'Complete',
 'batch_id': 'b7806cd9-86ff-4962-8503-45d45dd5d287',
 'batch_name': 'Bulk Send List for Job-25069',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': 'fa866db6-3211-4a86-b850-e4743127c726',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': '1',
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/b7806cd9-86ff-4962-8503-45d45dd5d287/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '9fd6c738-73bf-44b4-a95a-83513bf9442e',
 'mailing_list_name': 'Bulk Send List for Job-25069',
 'owner_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'queued': '0',
 'resends_remaining': '3',
 'sender_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'sent': '1',
 'submitted_date': '2025-04-25T15:57:05.7970000Z'}
2025-04-25 02:57:17,958 - docusign_logger - INFO - Batch size: 1
2025-04-25 02:57:18,292 - docusign_logger - INFO - ------------------Custom fields: {'list_custom_fields': [],
 'text_custom_fields': [{'configuration_type': None,
                         'error_details': None,
                         'field_id': '5124225651',
                         'name': 'Recipient Name',
                         'required': 'false',
                         'show': 'false',
                         'value': 'Matthew  Stapon'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5124225652',
                         'name': 'PRCo',
                         'required': 'false',
                         'show': 'false',
                         'value': '1'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5124225653',
                         'name': 'Job',
                         'required': 'false',
                         'show': 'false',
                         'value': '25069'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5124225654',
                         'name': 'Employee',
                         'required': 'false',
                         'show': 'false',
                         'value': '1187'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5124225655',
                         'name': 'BulkBatchId',
                         'required': 'false',
                         'show': 'false',
                         'value': 'b7806cd9-86ff-4962-8503-45d45dd5d287'}]} from envelope ID: 1a992807-2380-465d-9e80-2cfccc2af431
2025-04-25 02:57:18,298 - docusign_logger - INFO - Envelope data: [{'envelope_id': '1a992807-2380-465d-9e80-2cfccc2af431', 'PRCo': '1', 'Job': '25069', 'Employee': '1187'}], type: <class 'list'>
2025-04-25 02:57:18,299 - docusign_logger - INFO - Envelope IDs: ['1a992807-2380-465d-9e80-2cfccc2af431']
2025-04-25 02:57:19,925 - docusign_logger - INFO - Logs have been inserted into custom tracker successfully.
2025-04-25 02:57:19,933 - docusign_logger - INFO - Logging signer info on tracking table.
2025-04-25 02:57:22,243 - docusign_logger - INFO - UD fields cleaned up successfully.
2025-04-25 02:57:22,246 - docusign_logger - INFO - Docusign workflow completed successfully.
