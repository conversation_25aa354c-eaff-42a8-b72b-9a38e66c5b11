2025-04-25 02:54:52,163 - docusign_logger - INFO - DocuSign process started
2025-04-25 02:54:52,306 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 1, Job = 25069., Username = olsen.sql.p4m
2025-04-25 02:54:53,533 - docusign_logger - ERROR - ==================================================
2025-04-25 02:54:53,535 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-25 02:54:53,535 - docusign_logger - ERROR - Type: AttributeError
2025-04-25 02:54:53,535 - docusign_logger - ERROR - Message: 'str' object has no attribute 'get'
2025-04-25 02:54:53,536 - docusign_logger - ERROR - Traceback:
2025-04-25 02:54:53,537 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 346, in start_docusign_bulk_send_within_tkinter
    initialize_config(secret_data)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 275, in initialize_config
    Config.CONN_STR_VIEWPOINT = secret_data.get('VIEWPOINT_DB_URL')
AttributeError: 'str' object has no attribute 'get'

2025-04-25 02:54:53,537 - docusign_logger - ERROR - ==================================================
2025-04-25 02:54:53,539 - docusign_logger - INFO - Exception occurred: <class 'AttributeError'>, eSignatureapi_docusign_bulksend.py, 346
