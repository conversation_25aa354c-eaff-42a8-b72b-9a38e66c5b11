2025-04-25 02:49:48,422 - docusign_logger - INFO - DocuSign process started
2025-04-25 02:49:49,062 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 1, Job = 25069., Username = olsen.sql.p4m
2025-04-25 02:49:54,275 - docusign_logger - ERROR - ==================================================
2025-04-25 02:49:54,275 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-25 02:49:54,276 - docusign_logger - ERROR - Type: TypeError
2025-04-25 02:49:54,276 - docusign_logger - ERROR - Message: the JSON object must be str, bytes or bytearray, not dict
2025-04-25 02:49:54,278 - docusign_logger - ERROR - Traceback:
2025-04-25 02:49:54,278 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 345, in start_docusign_bulk_send_within_tkinter
    secret_data = extract_secrets_from_azure_key_vault(Config.AZURE_KEYVAULT_SECRET_VALUE)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\utils.py", line 93, in extract_secrets_from_azure_key_vault
    return json.loads(test)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\json\__init__.py", line 341, in loads
    raise TypeError(f'the JSON object must be str, bytes or bytearray, '
TypeError: the JSON object must be str, bytes or bytearray, not dict

2025-04-25 02:49:54,278 - docusign_logger - ERROR - ==================================================
2025-04-25 02:49:54,280 - docusign_logger - INFO - Exception occurred: <class 'TypeError'>, eSignatureapi_docusign_bulksend.py, 345
