2025-04-25 02:50:16,514 - docusign_logger - INFO - DocuSign process started
2025-04-25 02:50:16,654 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 1, Job = 25069., Username = olsen.sql.p4m
2025-04-25 02:50:17,667 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-25 02:50:19,860 - docusign_logger - INFO - BulkSend will send signature request to employee ranging from 1 to 3000
2025-04-25 02:50:20,589 - docusign_logger - INFO - Bulk send will send signature request to 66 employees.
2025-04-25 02:50:32,648 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2714}
2025-04-25 02:50:33,693 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiNjgxODVmZjEtNGU1MS00Y2U5LWFmMWMtNjg5ODEyMjAzMzE3In0.AQoAAAABAAUABwCAEjs91oPdSAgAgHr_nt6D3UgCAP8ljtpWL-tMrlOF5voKDVgVAAEAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.Vl6q4IlxDTUf_TwJL1Zj8PXb23zyMXoPl-ytufWDJSzFSzeDToAml8BfROH2iAM2VZVzHa8qf6NyjS75jbJ_AyGZySobuVemu-eXPsyz1CGAc_fPz6nMBM24GHc86Z2JgkhRTtOZJvmMDOfatMYQl_M-Vq0SyarEOKmDxohqYo_se9GfdJCKvwXu72tsBdVFj-C-wZCjbIXzB2JYVJuALcl8dM_c9jwecCq-QsQWJccYLpqeWRz1i0evMGZ482vSKMmD1NKT3GuEgh6DDPurWkeK7wayVVuUqBcCuGDeo9IFh5_N5OxFr6zBwmDCzUedrrxkejVo1GkOvX-beuZBMA
2025-04-25 02:50:33,695 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-25 02:50:47,418 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.pdf', 'doc_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.doc', 'success': True, 'error': None}
2025-04-25 02:50:47,425 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-25 02:50:53,826 - docusign_logger - INFO - Newly Created Batch status: {'action': 'Send',
 'action_status': 'Processing',
 'batch_id': '948a58bc-a270-4ea3-8928-6ce922cfde34',
 'batch_name': 'Bulk Send List for Job-25069',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': '5d483229-a1c7-45fd-ba3f-709f46749a3f',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': None,
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/948a58bc-a270-4ea3-8928-6ce922cfde34/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '28a08b8e-4e40-4690-99b9-76c28d64b8f9',
 'mailing_list_name': 'Bulk Send List for Job-25069',
 'owner_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'queued': '1',
 'resends_remaining': 'N/A',
 'sender_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'sent': '0',
 'submitted_date': '2025-04-25T15:50:53.1500000Z'} Batch ID: 948a58bc-a270-4ea3-8928-6ce922cfde34
2025-04-25 02:50:53,829 - docusign_logger - INFO - Bulk send initiated with batch ID: 948a58bc-a270-4ea3-8928-6ce922cfde34
2025-04-25 02:50:53,836 - docusign_logger - INFO - Bulk send initiated successfully with Batch ID: 948a58bc-a270-4ea3-8928-6ce922cfde34. Cleaning up the temporary files.
2025-04-25 02:50:53,839 - docusign_logger - INFO - Background process started to monitor batch and set reminders. Please wait.
2025-04-25 02:51:03,843 - docusign_logger - INFO - Checking for envelopes in batch (Attempt 1/80)...
2025-04-25 02:51:04,905 - docusign_logger - INFO - Batch status: {'action': 'Send',
 'action_status': 'Processing',
 'batch_id': '948a58bc-a270-4ea3-8928-6ce922cfde34',
 'batch_name': 'Bulk Send List for Job-25069',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': '5d483229-a1c7-45fd-ba3f-709f46749a3f',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': None,
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/948a58bc-a270-4ea3-8928-6ce922cfde34/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '28a08b8e-4e40-4690-99b9-76c28d64b8f9',
 'mailing_list_name': 'Bulk Send List for Job-25069',
 'owner_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'queued': '1',
 'resends_remaining': 'N/A',
 'sender_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'sent': '0',
 'submitted_date': '2025-04-25T15:50:53.1500000Z'}
2025-04-25 02:51:04,910 - docusign_logger - INFO - Batch not yet completed. Current action_status: Processing


2025-04-25 02:51:14,917 - docusign_logger - INFO - Checking for envelopes in batch (Attempt 2/80)...
2025-04-25 02:51:15,601 - docusign_logger - INFO - Batch status: {'action': 'Send',
 'action_status': 'Complete',
 'batch_id': '948a58bc-a270-4ea3-8928-6ce922cfde34',
 'batch_name': 'Bulk Send List for Job-25069',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': '5d483229-a1c7-45fd-ba3f-709f46749a3f',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': '1',
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/948a58bc-a270-4ea3-8928-6ce922cfde34/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '28a08b8e-4e40-4690-99b9-76c28d64b8f9',
 'mailing_list_name': 'Bulk Send List for Job-25069',
 'owner_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'queued': '0',
 'resends_remaining': '3',
 'sender_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'sent': '1',
 'submitted_date': '2025-04-25T15:50:53.1500000Z'}
2025-04-25 02:51:16,092 - docusign_logger - INFO - Batch size: 1
2025-04-25 02:51:16,515 - docusign_logger - INFO - ------------------Custom fields: {'list_custom_fields': [],
 'text_custom_fields': [{'configuration_type': None,
                         'error_details': None,
                         'field_id': '11193454982',
                         'name': 'Recipient Name',
                         'required': 'false',
                         'show': 'false',
                         'value': 'Ravino Johnson'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11193454983',
                         'name': 'PRCo',
                         'required': 'false',
                         'show': 'false',
                         'value': '1'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11193454984',
                         'name': 'Job',
                         'required': 'false',
                         'show': 'false',
                         'value': '25069'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11193454985',
                         'name': 'Employee',
                         'required': 'false',
                         'show': 'false',
                         'value': '2714'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11193454986',
                         'name': 'BulkBatchId',
                         'required': 'false',
                         'show': 'false',
                         'value': '948a58bc-a270-4ea3-8928-6ce922cfde34'}]} from envelope ID: 36ec2d9a-e84e-4b88-ae00-9073f5236bb3
2025-04-25 02:51:16,517 - docusign_logger - INFO - Envelope data: [{'envelope_id': '36ec2d9a-e84e-4b88-ae00-9073f5236bb3', 'PRCo': '1', 'Job': '25069', 'Employee': '2714'}], type: <class 'list'>
2025-04-25 02:51:16,517 - docusign_logger - INFO - Envelope IDs: ['36ec2d9a-e84e-4b88-ae00-9073f5236bb3']
2025-04-25 02:51:18,792 - docusign_logger - INFO - Logs have been inserted into custom tracker successfully.
2025-04-25 02:51:18,847 - docusign_logger - INFO - Logging signer info on tracking table.
2025-04-25 02:51:21,297 - docusign_logger - INFO - UD fields cleaned up successfully.
2025-04-25 02:51:21,306 - docusign_logger - INFO - Docusign workflow completed successfully.
