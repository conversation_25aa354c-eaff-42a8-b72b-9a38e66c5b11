2025-04-28 15:34:18,647 - docusign_logger - INFO - DocuSign process started
2025-04-28 15:34:19,135 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-04-28 15:34:20,346 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-28 15:34:23,191 - docusign_logger - INFO - Bulk send will send signature request to 75 employees.
2025-04-28 15:34:47,908 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2654}
2025-04-28 15:34:48,837 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwAADC6AnIbdSAgAAHTy4aSG3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.Ksy4sGYArKwFcjH4eX1fVYrKO52d4Tf3gkizlIdEukS9Xu3IhWkbV-iO8h90jT7CDWSDgamfzI0gT_5Szl0N-7Qo5DtmDCkQ5CwLZJKDS_EZ6orBgLwgRlZEdwqT4ihqCao0m3X3FD-ct-isAoix98DWFvZPHSAja_ziUFVORQKoi-2V96JUwS3G8HdYzZ_HYpGjliBKzcLtanlV4GVrc0zNnbzEs-zJkkv7GLp55cUD7NTPru9kpsJ-pP2q_CcmZNh5cx5EMFc0zUUr3GxjtMqHeFKdfskYthM93M0i05DCeVXXuv6revle6ua8Q0RM_a2Gm9qvuJaPPAZ5aIyQZQ
2025-04-28 15:34:48,838 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-28 15:34:58,933 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-04-28 15:34:58,936 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-28 15:34:58,951 - docusign_logger - ERROR - ==================================================
2025-04-28 15:34:58,951 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-28 15:34:58,951 - docusign_logger - ERROR - Type: FileNotFoundError
2025-04-28 15:34:58,951 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-04-28 15:34:58,951 - docusign_logger - ERROR - Traceback:
2025-04-28 15:34:58,952 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-04-28 15:34:58,952 - docusign_logger - ERROR - ==================================================
2025-04-28 15:34:58,953 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
