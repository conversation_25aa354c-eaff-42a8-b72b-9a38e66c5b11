2025-05-01 15:34:17,158 - docusign_logger - INFO - DocuSign process started
2025-05-01 15:34:17,315 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-05-01 15:34:18,599 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-05-01 15:34:21,565 - docusign_logger - INFO - Bulk send will send signature request to 74 employees.
2025-05-01 15:34:33,526 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2527}
2025-05-01 15:34:34,593 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwAAERP394jdSAgAAHnXWACJ3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.Vfl-an51sr4MiH18NLgw8QzgUHvPL53OqiTtWFgqrN4B6jVCC-syEW7qhXkxOGPt8-lxB41hZdLfxNu7SLa6935XjrmjCjXCvkv60OJ8f3v1uVO1d6tauS7qRNP0Jyd2uwAIIQrkRs2GPXGxJJFJjE2oyqJXZoDFXacCKaPs5TOV2UPtQIvSbo-0chbDuaywcuehWRslDYx1cyzPTS-XuzaPlSqGXhJbF3SrEjRzlMt-FNAlpDTxbWqPaqJv-UZIomiHebUnYYxfJBWxNqPIE-oRkXYhgmD85Z2xh3ULaz99cvu2Vzta2r8S6XYY-81xvuhanqrG3Bc9oHE7-05sZA
2025-05-01 15:34:34,593 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-05-01 15:34:42,701 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-05-01 15:34:42,701 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-05-01 15:34:42,701 - docusign_logger - ERROR - ==================================================
2025-05-01 15:34:42,701 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-05-01 15:34:42,701 - docusign_logger - ERROR - Type: FileNotFoundError
2025-05-01 15:34:42,701 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-05-01 15:34:42,701 - docusign_logger - ERROR - Traceback:
2025-05-01 15:34:42,701 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-05-01 15:34:42,701 - docusign_logger - ERROR - ==================================================
2025-05-01 15:34:42,701 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
