2025-04-28 15:57:45,032 - docusign_logger - INFO - DocuSign process started
2025-04-28 15:57:45,197 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-04-28 15:57:46,380 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-28 15:57:49,709 - docusign_logger - INFO - Bulk send will send signature request to 75 employees.
2025-04-28 15:58:40,832 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2564}
2025-04-28 15:58:41,830 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwCAflDWn4bdSAgAgOYUOKiG3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.D3VPH52Zvck_xGhhZ21ThR2uGFbaCylsg5Vqz_hgS_ERpNtib3xyHv5YnbqqMSSCeO0QTmz92daFFoWrNVkZjzRqxGR7SupJGKWTeNbNzFFMJeiRbpRK8C0BK_CfC7DUfGAH8k6OUum4VyRPSL22WMqd8AsEilbktlt_3IOMfdwLpF5kz_2Q5BGKyrek1gsZvywIxzfXR-Y6ZtRJ2vc_cYQUEowX8ZHfC1Cuo2CvfV6yvIeQVfOwWhtT--jqA0p1UkbXqtyzj0aS5KvW9_HXgc7-AX1FCzU50iPsz7UeH9EZ8FYTP6-pXdq4855lpVE7MUpMueU_EmoJqgOOoJ89Wg
2025-04-28 15:58:41,831 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-28 15:58:56,318 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-04-28 15:58:56,318 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-28 15:58:56,334 - docusign_logger - ERROR - ==================================================
2025-04-28 15:58:56,334 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-28 15:58:56,334 - docusign_logger - ERROR - Type: FileNotFoundError
2025-04-28 15:58:56,334 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-04-28 15:58:56,334 - docusign_logger - ERROR - Traceback:
2025-04-28 15:58:56,334 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-04-28 15:58:56,334 - docusign_logger - ERROR - ==================================================
2025-04-28 15:58:56,334 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
