Dim Arg, var1, var2, var3, scriptdir, parentdir, filesys
Set Arg = WScript.Arguments
var1 = """" & Arg(0) & """"
var2 = """" & Arg(1) & """"

Set filesys = CreateObject("Scripting.FileSystemObject")
scriptdir = filesys.GetParentFolderName(WScript.ScriptFullName)
parentdir = filesys.GetParentFolderName(filesys.GetParentFolderName(WScript.ScriptFullName))
' Set FSO = CreateObject("Scripting.FileSystemObject")
' Set File = FSO.CreateTextFile("C:\test.txt",True)
' File.Write scriptdir & "\Portable\App\Python\python.exe" & " " & scriptdir & "\preview_template.py " & var1 & " " & var2 & " " & var3 & " " & var4 & " " & var5 & " " & var6
' File.Close

dim fname
fname=InputBox("Enter your name:", "Test", parentdir & "\Kernel\App\Python\python.exe" & " " & scriptdir & "\preview_template.py " & var1 & " " & var2)


' Set WshShell = CreateObject("WScript.Shell" )
' WshShell.Run parentdir & "\Kernel\App\Python\python.exe" & " " & scriptdir & "\preview_template.py " & var1 & " " & var2, 0, False

'Clear the objects at the end of your script.
set Arg = Nothing
Set WshShell = Nothing