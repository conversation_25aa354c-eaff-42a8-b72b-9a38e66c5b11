2025-05-20 16:08:40,952 - docusign_logger - INFO - DocuSign process started
2025-05-20 16:08:41,199 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 24140. 5, Username = olsen.sql.p4m
2025-05-20 16:08:42,572 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-05-20 16:08:45,742 - docusign_logger - INFO - Bulk send will send signature request to 76 employees.
2025-05-20 16:08:57,139 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2904}
2025-05-20 16:08:58,065 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwCAIpHq6pfdSAgAgIpVTPOX3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.Uej25jsZjQJn2XuqTbA7JYvntb_ppW-YZQPVUCiWal3y9fGn1cOBtcG71rGEpuNj8L8Lt--Es_HZcIEoBpx3acJ62PaxoyFwsdnvfzXIqsTCX7KzF2RSZBMEk8YZSCMOUChFyLWsLvdHA7sDgiEEGaLTgexTlHvgQRnkxQ3sfCQY_fhOAfTWCh8QfMJ4mM1z5L1EQIMMyUGwTJ6E0M0q_fiNuAap_BDjwcvkEZkw1hUy8F5Io8F_VEermf_AbSVNmx_iwptC5Y14KUDhNen38SDq7gu1A7Tx6No9QOyYjOaoWr0AvDB64JHDz_pxd2G80vXKdEe6IbpbALy5teGgPA
2025-05-20 16:08:58,066 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-05-20 16:09:09,198 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.pdf', 'doc_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.doc', 'success': True, 'error': None}
2025-05-20 16:09:09,204 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-05-20 16:09:14,634 - docusign_logger - INFO - Newly Created Batch status: {'action': 'Send',
 'action_status': 'Processing',
 'batch_id': '254706ad-2d37-43ac-8b55-8c4e545cafef',
 'batch_name': 'Bulk Send List for Job-241405',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': 'cf59d253-74f9-4cf9-a66d-76c067b4f449',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': None,
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/254706ad-2d37-43ac-8b55-8c4e545cafef/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '2aaf9013-20df-4184-b641-2b55e3e66d7b',
 'mailing_list_name': 'Bulk Send List for Job-241405',
 'owner_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'queued': '1',
 'resends_remaining': 'N/A',
 'sender_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'sent': '0',
 'submitted_date': '2025-05-21T05:09:13.8070000Z'} Batch ID: 254706ad-2d37-43ac-8b55-8c4e545cafef
2025-05-20 16:09:14,636 - docusign_logger - INFO - Bulk send initiated with batch ID: 254706ad-2d37-43ac-8b55-8c4e545cafef
2025-05-20 16:09:14,637 - docusign_logger - INFO - Bulk send initiated successfully with Batch ID: 254706ad-2d37-43ac-8b55-8c4e545cafef. Cleaning up the temporary files.
2025-05-20 16:09:14,642 - docusign_logger - INFO - Background process started to monitor batch and set reminders. Please wait.
2025-05-20 16:09:24,646 - docusign_logger - INFO - Checking for envelopes in batch (Attempt 1/80)...
2025-05-20 16:09:25,960 - docusign_logger - INFO - Batch status: {'action': 'Send',
 'action_status': 'Complete',
 'batch_id': '254706ad-2d37-43ac-8b55-8c4e545cafef',
 'batch_name': 'Bulk Send List for Job-241405',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': 'cf59d253-74f9-4cf9-a66d-76c067b4f449',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': '1',
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/254706ad-2d37-43ac-8b55-8c4e545cafef/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '2aaf9013-20df-4184-b641-2b55e3e66d7b',
 'mailing_list_name': 'Bulk Send List for Job-241405',
 'owner_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'queued': '0',
 'resends_remaining': '3',
 'sender_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'sent': '1',
 'submitted_date': '2025-05-21T05:09:13.8070000Z'}
2025-05-20 16:09:26,335 - docusign_logger - INFO - Batch size: 1
2025-05-20 16:09:26,712 - docusign_logger - INFO - ------------------Custom fields: {'list_custom_fields': [],
 'text_custom_fields': [{'configuration_type': None,
                         'error_details': None,
                         'field_id': '5167072815',
                         'name': 'Recipient Name',
                         'required': 'false',
                         'show': 'false',
                         'value': 'Christopher Allfrey'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5167072816',
                         'name': 'PRCo',
                         'required': 'false',
                         'show': 'false',
                         'value': '5'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5167072817',
                         'name': 'Job',
                         'required': 'false',
                         'show': 'false',
                         'value': '241405'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5167072818',
                         'name': 'Employee',
                         'required': 'false',
                         'show': 'false',
                         'value': '2904'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5167072819',
                         'name': 'BulkBatchId',
                         'required': 'false',
                         'show': 'false',
                         'value': '254706ad-2d37-43ac-8b55-8c4e545cafef'}]} from envelope ID: b46dfadf-09bc-4959-88b1-a67414aff64a
2025-05-20 16:09:26,715 - docusign_logger - INFO - Envelope data: [{'envelope_id': 'b46dfadf-09bc-4959-88b1-a67414aff64a', 'PRCo': '5', 'Job': '241405', 'Employee': '2904'}], type: <class 'list'>
2025-05-20 16:09:26,716 - docusign_logger - INFO - Envelope IDs: ['b46dfadf-09bc-4959-88b1-a67414aff64a']
2025-05-20 16:09:28,504 - docusign_logger - INFO - Logs have been inserted into custom tracker successfully.
2025-05-20 16:09:28,507 - docusign_logger - INFO - Logging signer info on tracking table.
2025-05-20 16:09:31,448 - docusign_logger - INFO - UD fields cleaned up successfully.
2025-05-20 16:09:31,460 - docusign_logger - INFO - Docusign workflow completed successfully.
