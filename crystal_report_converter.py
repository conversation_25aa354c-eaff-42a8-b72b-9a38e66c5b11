import subprocess
import json
import os
import re
from datetime import datetime

class CrystalReportConverter:
    def __init__(self, executable_path):
        """
        Initialize the Crystal Report Converter.
        
        Args:
            executable_path (str): Path to the CrystalReportDemo.exe executable
        """
        if not executable_path or executable_path == "":
            base_dir = os.path.dirname(os.path.realpath(__file__))
            self.executable_path = os.path.join(base_dir, "CrystalReportManager", "CrystalReportDemo.exe")
        else:
            self.executable_path = executable_path
        
    def generate_report(self, template_name, parameters):
        """
        Generate a report using the Crystal Report Converter.
        
        Args:
            template_name (str): Name of the report template without .rpt extension
            parameters (dict): Dictionary of parameter names and values
            
        Returns:
            dict: Dictionary containing paths to generated PDF and DOC files
        """
        serializable_parameters = {}
        for key, value in parameters.items():
            if isinstance(value, datetime):
                # Convert datetime objects to formatted strings
                serializable_parameters[key] = value.strftime('%m/%d/%Y %I:%M:%S %p')
            else:
                serializable_parameters[key] = value

        params_json = json.dumps(serializable_parameters)
        
        # Run the C# application with arguments
        try:
            cmd = [self.executable_path, template_name, params_json]
            print(f"Executing command: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True
            )
            
            # Parse the output to extract file paths
            output = result.stdout
            
            # Look for the special marker in the output
            json_output = None
            if "***###" in output:
                # Extract the JSON part after the marker
                json_text = output.split("***###")[-1].strip()
                
                # Find the JSON object using regex
                match = re.search(r'(\{.*\})', json_text, re.DOTALL)
                if match:
                    json_output = json.loads(match.group(1))
            
            if json_output:
                return {
                    'pdf_path': json_output['PdfFilePath'],
                    'doc_path': json_output['DocFilePath'],
                    'success': True,
                    'error': None
                }
            else:
                return {
                    'pdf_path':'',
                    'doc_path': '',
                    'success': False,
                    'error': 'Failed to generate the report. Please check the Crystal report logs.'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'pdf_path':'',
                'doc_path': ''
            }

    def verify_files_exist(self, result):
        """
        Verify that the generated files exist.
        
        Args:
            result (dict): Result from generate_report
            
        Returns:
            bool: True if files exist, False otherwise
        """
        if not result['success']:
            return False
            
        pdf_exists = os.path.exists(result['pdf_path'])
        doc_exists = os.path.exists(result['doc_path'])
        
        return pdf_exists and doc_exists
