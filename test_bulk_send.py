import base64
from docusign_esign import *
#from auth import _get_JWT_token
from config import Config
import time
from cryptography.hazmat.primitives import serialization as crypto_serialization
import os
from docusign_esign import RecipientViewRequest, EnvelopeDefinition, Document, Signer, SignHere, Approve, Tabs, Recipients, ApiClient, EnvelopesApi, Text as CustomField, DateSigned, CarbonCopy, InitialHere, RecipientEmailNotification
from cryptography.hazmat.primitives import serialization
import requests
import redis
import json
from azure.identity import CertificateCredential
from azure.keyvault.secrets import SecretClient
import jwt

TTL_SECONDS = 3600*4    #Storing in redis for 4 hours 


def initialize_config(secret_data):
    # Database URLs
    Config.CONN_STR_VIEWPOINT = secret_data.get('VIEWPOINT_DB_URL')
    Config.CONN_STR_VPATTACHMENT = secret_data.get('VP_ATTACHMENTS_DB_URL')
    Config.CONN_STR_SMART_SIGN = secret_data.get('ESIGNATURE_DB_URL')
    
    # DocuSign configuration
    Config.DS_INTEGRATION_KEY = secret_data.get('DS_INTEGRATION_KEY')
    Config.DS_ACCOUNT_ID = secret_data.get('DS_ACCOUNT_ID')
    Config.DS_USER_ID = secret_data.get('DS_USER_ID')
    Config.DS_PRIVATE_KEY_FILE = secret_data.get('DS_PRIVATE_KEY_FILE').encode('utf-8')
    Config.DS_AUTHORIZATION_SERVER = secret_data.get('DS_AUTHORIZATION_SERVER')
    Config.DS_BASE_URL = secret_data.get('DS_BASE_URL')
    Config.DS_ACCOUNT_URL = secret_data.get('DS_ACCOUNT_URL')
    Config.DS_BRAND_ID = secret_data.get('DS_BRAND_ID')
    Config.DS_BASE_PATH = secret_data.get('DS_BASE_PATH') + '/restapi'

def docusign_token():
    iat = int(time.time())  # Convert to integer
    exp = iat + (3600 * 24)  # 24 hours token validity

    payload = {
        "sub": Config.DS_USER_ID,
        "iss": Config.DS_INTEGRATION_KEY,
        "iat": iat,
        "exp": exp,
        "aud": Config.DS_AUTHORIZATION_SERVER,
        "scope": "signature"
    }

     # No need to encode, Config.DS_PRIVATE_KEY_FILE is already bytes
    private_key_bytes = Config.DS_PRIVATE_KEY_FILE

    # Load the private key
    private_key = serialization.load_pem_private_key(
        private_key_bytes,
        password=None
    )

    # Convert private key to PEM format
    key = private_key.private_bytes(
        serialization.Encoding.PEM,
        serialization.PrivateFormat.PKCS8,
        serialization.NoEncryption()
    )

    # Generate JWT using PyJWT (correct way)
    jwt_token = jwt.encode(payload, key, algorithm="RS256")

    return jwt_token

def get_secret_from_redis(secret_name):
    redis_host = Config.REDIS_HOST
    redis_port = Config.REDIS_PORT
    redis_key = Config.REDIS_ACCESS_KEY
    r = redis.Redis(host=redis_host, port=redis_port, db=0, password=redis_key,  ssl=True)
    print('Getting secret for EarleCo. Secret:', secret_name)
    return r.get(secret_name)

def store_in_redis(secret_name, secret_value):
    redis_host = Config.REDIS_HOST
    redis_port = Config.REDIS_PORT
    redis_key = Config.REDIS_ACCESS_KEY
    r = redis.Redis(host=redis_host, port=redis_port, db=0, password=redis_key, ssl=True)
    r.set(secret_name, secret_value)
    r.expire(secret_name, TTL_SECONDS)
    print('Secret for EarleCo has been Stored in Redis')

def extract_secrets_from_azure_key_vault(secret_name):
    secret_value = get_secret_from_redis(secret_name)
    
    if secret_value:
        print(f"Found secret {secret_name} in Redis cache")
        decoded_value = secret_value.decode('utf-8')  # Redis returns bytes, need to decode
        try:
            return json.loads(decoded_value)
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON: {e}")
            return decoded_value
    
    # Authenticate using the certificate
    credential = CertificateCredential(
        tenant_id=Config.TENANT_ID,
        client_id=Config.CLIENT_APPLICATION_ID,
        certificate_path=os.path.join(os.path.dirname(__file__), Config.CERTIFICATE_PATH),
        password=Config.CERTIFICATE_PASSWORD
    )
    client = SecretClient(vault_url=Config.AZURE_KEY_VAULT_URL, credential=credential)
    
    print(f"Fetching secret {secret_name} from Azure Key Vault")
    secret = client.get_secret(secret_name)
    
    # Store in Redis for future use
    store_in_redis(secret_name, secret.value)
    return json.loads(secret.value)

def create_jwt_grant_token():
    token = docusign_token()
    return token

def initialize_docusign_api():
    #getting docusign token, creating api client
    token = create_jwt_grant_token()
    post_data = {'grant_type': 'urn:ietf:params:oauth:grant-type:jwt-bearer', 'assertion': token} 
    base_url = 'https://' + Config.DS_AUTHORIZATION_SERVER + '/oauth/token'
    r = requests.post(base_url, data=post_data)
    token = r.json()
    api_client = ApiClient()
    api_client.host = "https://demo.docusign.net/restapi"
    api_client.set_default_header('Authorization', 'Bearer ' + token["access_token"])
    envelopes_api = EnvelopesApi(api_client)
    return envelopes_api


def create_bulk_sending_list(args):
  bulk_copies = []
  for signer in args:
      recipient_1 = BulkSendingCopyRecipient(
          role_name="signer",
          name=signer["signer_name"],
          email=signer["signer_email"],
          tabs=[
               BulkSendingCopyTab(
                    tab_label = "SignerName",
                    initial_value = signer["signer_name"]
               ),
               BulkSendingCopyTab(
                    tab_label = "SignerPhone",
                    initial_value = signer["signer_phone"]
               ),
               BulkSendingCopyTab(
                    tab_label = "SignerEmail",
                    initial_value = signer["signer_email"]
                )
          ]
      )

      bulk_copy = BulkSendingCopy(
          recipients=[recipient_1],
          custom_fields=[]
      )

      bulk_copies.append(bulk_copy)

  bulk_sending_list = BulkSendingList(
      name="sample",
      bulk_copies=bulk_copies
  )

  return bulk_sending_list

def make_draft_envelope():
    # Open the example file
    with open("C:/ViewpointIntegrationEarle/ViewpointDocusignBulkPREH/EAC MW212.pdf", "rb") as file:
        content_bytes = file.read()
    base64_file_content = base64.b64encode(content_bytes).decode("ascii")

    document = Document(
        document_base64=base64_file_content,
        name="Test Doc",
        file_extension="pdf",
        document_id=2
    )

    # Add placeholder tabs
    recipient_sign_here = SignHere(
        anchor_string="/sig1/",
        anchor_units="pixels",
        anchor_y_offset="0",
        anchor_x_offset="0",
        tab_label="RecipentTab"
    )
    
    recipient_text_name = Text(
        anchor_string="/n1/",
        anchor_units="pixels",
        anchor_x_offset="0",
        anchor_y_offset="-2",
        font="Arial",
        font_size="Size10",
        locked="true",
        tab_label="SignerName",
        width="150"
    )
    
    recipient_text_phone = Text(
        anchor_string="/c1/",
        anchor_units="pixels",
        anchor_y_offset="0",
        anchor_x_offset="-2",
        font="Arial",
        font_size="Size10",
        locked="true",
        tab_label="SignerPhone",
        width="150"
    )

    recipient_text_email = Text(
        anchor_string="/e1/",
        anchor_units="pixels",
        anchor_y_offset="0",
        anchor_x_offset="-2",
        font="Arial",
        font_size="Size10",
        locked="true",
        tab_label="SignerEmail",
        width="150"
    )

    # Add placeholder recipients
    signer = Signer(
        name="Multi Bulk Recipient::signer",
        email="<EMAIL>",
        role_name="signer",
        routing_order="1",
        recipient_id="1",
    )

    signer.tabs = Tabs(sign_here_tabs=[recipient_sign_here], text_tabs=[recipient_text_name, recipient_text_phone, recipient_text_email])

    envelope_definition = EnvelopeDefinition(
        email_subject="Please Sign",
        documents=[document],
        status="created"
    )

    envelope_definition.recipients = Recipients(signers=[signer])

    return envelope_definition

signerName1="Sunny"
signerEmail1="<EMAIL>"
signerPhone1="**********"
signerName2="Tanvir"
signerEmail2="<EMAIL>"
signerPhone2="*********"

signers = [
            {
              "signer_name": signerName1,
              "signer_email": signerEmail1,
              "signer_phone": signerPhone1
            },
            {
              "signer_name": signerName2,
              "signer_email": signerEmail2,
              "signer_phone": signerPhone2
            }
          ]

api_client = ApiClient()

secret_data = extract_secrets_from_azure_key_vault(Config.AZURE_KEYVAULT_SECRET_VALUE)
initialize_config(secret_data)

envelopes_api = initialize_docusign_api()
access_token = envelopes_api.api_client.default_headers["Authorization"].split("Bearer ")[1]
api_base_path = "https://demo.docusign.net/restapi"
account_id = "e1e226d7-9024-42f1-a2af-253c39d7eb7d"

# Construct your API headers
api_client.host = api_base_path
api_client.set_default_header(header_name="Authorization", header_value=f"Bearer {access_token}")

# Submit a bulk list
bulk_envelopes_api = BulkEnvelopesApi(api_client)
bulk_sending_list = create_bulk_sending_list(signers)
bulk_list = bulk_envelopes_api.create_bulk_send_list(
    account_id = account_id,
    bulk_sending_list = bulk_sending_list
)
bulk_list_id = bulk_list.list_id

# Create an envelope
envelope_api = EnvelopesApi(api_client)
envelope_definition = make_draft_envelope()
envelope = envelope_api.create_envelope(account_id = account_id, envelope_definition = envelope_definition)
envelope_id = envelope.envelope_id

# Attach your bulk list id to the envelope
text_custom_fields = TextCustomField(name="mailingListId", required="false", show="false", value=bulk_list_id)
custom_fields = CustomFields(list_custom_fields=[], text_custom_fields=[text_custom_fields])
envelope_api.create_custom_fields(
    account_id = account_id,
    envelope_id = envelope_id,
    custom_fields = custom_fields
)

# Initiate bulk send
bulk_send_request = BulkSendRequest(envelope_or_template_id=envelope_id)
batch = bulk_envelopes_api.create_bulk_send_request(
    account_id = account_id,
    bulk_send_list_id = bulk_list_id,
    bulk_send_request = bulk_send_request
)
batch_id = batch.batch_id

# Confirm successful batch send
response = bulk_envelopes_api.get_bulk_send_batch_status(account_id = account_id, bulk_send_batch_id = batch_id)
print(response)
