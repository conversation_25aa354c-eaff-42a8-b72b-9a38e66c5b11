2025-05-02 02:16:36,456 - docusign_logger - INFO - DocuSign process started
2025-05-02 02:16:36,632 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-05-02 02:16:37,970 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-05-02 02:16:40,916 - docusign_logger - INFO - Bulk send will send signature request to 74 employees.
2025-05-02 02:16:45,648 - docusign_logger - INFO - Final: Bulk send will send signature request to 0 employees: set()
2025-05-02 02:16:46,751 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwAAq--tUYndSAgAABO0D1qJ3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.AK5y5dLOuw1LXTWLkBnWshdNeon99nXUMc6OPCgny57RIALzw4xhy-Php2LISCmfZPyBY5u7YtImuxNwE0SA9WLzlk4AZZyqcQZNFiEn6W539wfI65O5giQyRwnHf-XtAoRDztPhN-NgsROo-PkucUo1HONjqcvi_6UtY-GAjb_7DQdnzP9w7DpxiCsvGkiKiZ7mFKz1rEbQ_pEr53xJ2P1VdK_O3I8QiZXLu4R47aqju7g9UC3VQzlxJnzcG90JciBSQWv8cslcc9ZL4dbqfiQJGMLY_ZLUPV0loO_4pD0LfVsDDrm8NxHUnTjg5t3hFSB5kqzB0wTRS92hhenFug
2025-05-02 02:16:46,755 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-05-02 02:16:46,769 - docusign_logger - ERROR - ==================================================
2025-05-02 02:16:46,769 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-05-02 02:16:46,769 - docusign_logger - ERROR - Type: IndexError
2025-05-02 02:16:46,770 - docusign_logger - ERROR - Message: list index out of range
2025-05-02 02:16:46,770 - docusign_logger - ERROR - Traceback:
2025-05-02 02:16:46,770 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 445, in start_docusign_bulk_send_within_tkinter
    "AwardDate":  'USE_DEFAULT' if employee_details[0]['udJobAwardDate'] is None else employee_details[0]['udJobAwardDate'],
IndexError: list index out of range

2025-05-02 02:16:46,771 - docusign_logger - ERROR - ==================================================
2025-05-02 02:16:46,772 - docusign_logger - INFO - Exception occurred: <class 'IndexError'>, eSignatureapi_docusign_bulksend.py, 445
