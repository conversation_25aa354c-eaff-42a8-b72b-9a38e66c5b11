import redis
import os
import sys
import shutil
from PyPDF2 import PdfFileReader, PdfFileWriter
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from io import BytesIO
from azure.keyvault.secrets import SecretClient
from azure.identity import ClientSecretCredential
from config import *
from itertools import cycle
import json

print('utils.py')

TTL_SECONDS = 3600*4    #Storing in redis for 4 hours 


def store_in_redis(secret_name, secret_value):
    redis_host = Config.REDIS_HOST
    redis_port = Config.REDIS_PORT
    redis_key = Config.REDIS_ACCESS_KEY
    r = redis.Redis(host=redis_host, port=redis_port, db=0, password=redis_key, ssl=True)
    r.set(secret_name, secret_value)
    r.expire(secret_name, TTL_SECONDS)
    print('Secret for EarleCo has been Stored in Redis')

def get_secret_from_redis(secret_name):
    redis_host = Config.REDIS_HOST
    redis_port = Config.REDIS_PORT
    redis_key = Config.REDIS_ACCESS_KEY
    r = redis.Redis(host=redis_host, port=redis_port, db=0, password=redis_key,  ssl=True)
    print('Getting secret for EarleCo. Secret:', secret_name)
    return r.get(secret_name)

def update_status_panel(status_text, master, text):
    if status_text is None:
        return
    status_text.insert('1.0', "\r\n" + text)
    master.update()

def parse_args():
    sys_args = sys.argv
    sys_args = sys_args[1:]
    args_dict = {}
    for arg in sys_args:
        if '=' not in arg :
            continue
        key, value = arg.split("=", 1)
        args_dict[key] = value
    return args_dict

def delete_contents_of_dir(directory):
    for filename in os.listdir(directory):
        file_path = os.path.join(directory, filename)
        try:
            if os.path.isfile(file_path) or os.path.islink(file_path):
                os.unlink(file_path)  # remove file or symlink
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)  # remove directory
        except Exception as e:
            print(f'Failed to delete {file_path}. Reason: {e}')

def extract_secrets_from_azure_key_vault(secret_name):
    secret_value = get_secret_from_redis(secret_name)
    
    if secret_value:
        print(f"Found secret {secret_name} in Redis cache")
        decoded_value = secret_value.decode('utf-8')  # Redis returns bytes, need to decode
        try:
            return json.loads(decoded_value)
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON: {e}")
            return decoded_value
    
    # Authenticate using the certificate
    credential = ClientSecretCredential(
        tenant_id=Config.TENANT_ID,
        client_id=Config.CLIENT_APPLICATION_ID,
        client_secret=Config.CLIENT_APPLICATION_SECRET
    )
    client = SecretClient(vault_url=Config.AZURE_KEY_VAULT_URL, credential=credential)
    
    print(f"Fetching secret {secret_name} from Azure Key Vault")
    secret = client.get_secret(secret_name)
    
    # Store in Redis for future use
    store_in_redis(secret_name, secret.value)

    try:
        return json.loads(secret.value)
    except json.JSONDecodeError:
        # If not JSON, return the plain string
        return secret.value

class TestEmailManager:
    """Manages test email distribution"""
    def __init__(self):
        self.test_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ]
        self.email_cycle = cycle(self.test_emails)
    
    def get_next_email(self):
        """Get next test email in rotation"""
        return next(self.email_cycle)
    
    def dummy_add_employee_number(self, template_path, employee_number, output_path):
        packet = BytesIO()
        c = canvas.Canvas(packet, pagesize=letter)
        c.drawString(256, 750, f"Employee Number: {employee_number}")
        c.save()
        packet.seek(0)
        overlay = PdfReader(packet)
        existing_pdf = PdfReader(open(template_path, "rb"))
        output = PdfWriter()
        page = existing_pdf.pages[0]
        page.merge_page(overlay.pages[0])
        output.add_page(page)
        for page in existing_pdf.pages[1:]:
            output.add_page(page)
        with open(output_path, "wb") as output_file:
            output.write(output_file)
    

