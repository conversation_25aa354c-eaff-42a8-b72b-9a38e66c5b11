2025-04-28 16:02:49,778 - docusign_logger - INFO - DocuSign process started
2025-04-28 16:02:49,917 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-04-28 16:02:51,373 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-28 16:02:54,660 - docusign_logger - INFO - Bulk send will send signature request to 75 employees.
2025-04-28 16:03:00,482 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2564}
2025-04-28 16:03:01,445 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwCAWElxoIbdSAgAgMAN06iG3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.EPyB_f_k1T6-fPBzZtXlUhyptLiIs-cMyPykqLmdymYfreaGayHjLN0bUd-RVAIdmE7_NNRekbsxX__eN_YGVM77ry4xz8upT3JfEfnu15kQidkl_w39AXY-2gY5IjPR541WqF-5wh9MULKSiTH7Cph2XM1CP6MFgFjLFJCew7ZVGLmjY-WlgeOoLdQGp8RwGrQ09Of5YEaay99y7MshZpysbthNy778rpTVcGn5bra_v2orl4CTKZkzE09LwXwsK7GmiDx08Addts5BuAsbKdMJLZ7Ztrr52rpXSbl6UEVVZYIz-nDuVc53La6N4Mdc3TWqAqVAR8Q8diEay-2MLA
2025-04-28 16:03:01,447 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-28 16:03:10,167 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-04-28 16:03:10,169 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-28 16:03:10,175 - docusign_logger - ERROR - ==================================================
2025-04-28 16:03:10,176 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-28 16:03:10,176 - docusign_logger - ERROR - Type: FileNotFoundError
2025-04-28 16:03:10,176 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-04-28 16:03:10,176 - docusign_logger - ERROR - Traceback:
2025-04-28 16:03:10,176 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-04-28 16:03:10,176 - docusign_logger - ERROR - ==================================================
2025-04-28 16:03:10,177 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
