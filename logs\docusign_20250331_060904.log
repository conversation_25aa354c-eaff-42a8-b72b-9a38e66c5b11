2025-03-31 06:09:04,475 - docusign_logger - INFO - DocuSign process started
2025-03-31 06:09:04,619 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 1, Job = 20095.SA, Username = olsen.sql.p4m
2025-03-31 06:09:06,169 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-03-31 06:09:09,179 - docusign_logger - INFO - Bulk send will send signature request to 6 employees.
2025-03-31 06:09:10,334 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiNjgxODVmZjEtNGU1MS00Y2U5LWFmMWMtNjg5ODEyMjAzMzE3In0.AQoAAAABAAUABwAAN_3XTHDdSAgAAJ_BOVVw3UgCAP8ljtpWL-tMrlOF5voKDVgVAAEAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.ZyOm6kGsOrFSx5luYXmMFuaE95F8jFsfE56GVB5ZJr7rKeC7q7-2l_-0SLpE8ci09z0zVulfsEcu5cuLDtDqugdLWQj2M-HG9DZ9Zvnj7kT48F0SV-tekJJjlcSSideNuQ2sQf5gn4549XAvZ-huFizLZnvIHFjg6kQdsgevhw8XSQyKOrksEWajJFZE1SqW-ijsE7oiNTRgQXLocFpAoL6b8xXBIkrfGeeqdAMSxdMd0Rfgw96e_oRx8H4D8r8Xy_N7q3_1rI23fUGCMEAd_2YE1f3icH2-_qB3cj1x5o5qjjcs2jBlXaYDCHJWMgnwVd9mFTqTSEpOWwW512cqzg
2025-03-31 06:09:10,344 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-03-31 06:09:21,697 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': 'C:\\ViewpointIntegrationEarle\\CrystalReportManager\\Export\\EAC MW212.pdf', 'doc_path': 'C:\\ViewpointIntegrationEarle\\CrystalReportManager\\Export\\EAC MW212.doc', 'success': True, 'error': None}
2025-03-31 06:09:21,699 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-03-31 06:09:29,647 - docusign_logger - INFO - Newly Created Batch status: {'action': 'Send',
 'action_status': 'Processing',
 'batch_id': '3855f70f-bbb6-478c-98be-c1e3a0a4aaf7',
 'batch_name': 'Bulk Send List for Job-20095.SA',
 'batch_size': '6',
 'bulk_errors': [],
 'envelope_id_or_template_id': '0c1aa9ab-ef8e-4c93-9b0d-af87a982d9d6',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': None,
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/3855f70f-bbb6-478c-98be-c1e3a0a4aaf7/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '858c3895-4fae-47a2-9fb4-ce15ee4440e9',
 'mailing_list_name': 'Bulk Send List for Job-20095.SA',
 'owner_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'queued': '6',
 'resends_remaining': 'N/A',
 'sender_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'sent': '0',
 'submitted_date': '2025-03-31T19:09:28.7730000Z'} Batch ID: 3855f70f-bbb6-478c-98be-c1e3a0a4aaf7
2025-03-31 06:09:29,647 - docusign_logger - INFO - Bulk send initiated with batch ID: 3855f70f-bbb6-478c-98be-c1e3a0a4aaf7
2025-03-31 06:09:29,647 - docusign_logger - INFO - Bulk send initiated successfully with Batch ID: 3855f70f-bbb6-478c-98be-c1e3a0a4aaf7. Cleaning up the temporary files.
2025-03-31 06:09:29,663 - docusign_logger - INFO - Background process started to monitor batch and set reminders. Please wait.
2025-03-31 06:09:39,679 - docusign_logger - INFO - Checking for envelopes in batch (Attempt 1/80)...
2025-03-31 06:09:40,917 - docusign_logger - INFO - Batch status: {'action': 'Send',
 'action_status': 'Complete',
 'batch_id': '3855f70f-bbb6-478c-98be-c1e3a0a4aaf7',
 'batch_name': 'Bulk Send List for Job-20095.SA',
 'batch_size': '6',
 'bulk_errors': [],
 'envelope_id_or_template_id': '0c1aa9ab-ef8e-4c93-9b0d-af87a982d9d6',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': '6',
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/3855f70f-bbb6-478c-98be-c1e3a0a4aaf7/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '858c3895-4fae-47a2-9fb4-ce15ee4440e9',
 'mailing_list_name': 'Bulk Send List for Job-20095.SA',
 'owner_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'queued': '0',
 'resends_remaining': '3',
 'sender_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'sent': '6',
 'submitted_date': '2025-03-31T19:09:28.7730000Z'}
2025-03-31 06:09:41,431 - docusign_logger - INFO - Batch size: 6
2025-03-31 06:09:41,699 - docusign_logger - INFO - ------------------Custom fields: {'list_custom_fields': [],
 'text_custom_fields': [{'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186332',
                         'name': 'Recipient Name',
                         'required': 'false',
                         'show': 'false',
                         'value': 'William  Jeffers'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186333',
                         'name': 'PRCo',
                         'required': 'false',
                         'show': 'false',
                         'value': '1'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186334',
                         'name': 'Job',
                         'required': 'false',
                         'show': 'false',
                         'value': '20095.SA'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186335',
                         'name': 'Employee',
                         'required': 'false',
                         'show': 'false',
                         'value': '2117'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186336',
                         'name': 'BulkBatchId',
                         'required': 'false',
                         'show': 'false',
                         'value': '3855f70f-bbb6-478c-98be-c1e3a0a4aaf7'}]} from envelope ID: f2cfe5d2-12a9-484e-bc03-34600e28b6ef
2025-03-31 06:09:41,957 - docusign_logger - INFO - ------------------Custom fields: {'list_custom_fields': [],
 'text_custom_fields': [{'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186301',
                         'name': 'Recipient Name',
                         'required': 'false',
                         'show': 'false',
                         'value': 'Marc Cogliano'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186302',
                         'name': 'PRCo',
                         'required': 'false',
                         'show': 'false',
                         'value': '1'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186303',
                         'name': 'Job',
                         'required': 'false',
                         'show': 'false',
                         'value': '20095.SA'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186304',
                         'name': 'Employee',
                         'required': 'false',
                         'show': 'false',
                         'value': '2262'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186305',
                         'name': 'BulkBatchId',
                         'required': 'false',
                         'show': 'false',
                         'value': '3855f70f-bbb6-478c-98be-c1e3a0a4aaf7'}]} from envelope ID: 4ae52ad5-d7fb-4b36-bd3c-a7e0de5527b1
2025-03-31 06:09:42,209 - docusign_logger - INFO - ------------------Custom fields: {'list_custom_fields': [],
 'text_custom_fields': [{'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186296',
                         'name': 'Recipient Name',
                         'required': 'false',
                         'show': 'false',
                         'value': 'John Damas'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186297',
                         'name': 'PRCo',
                         'required': 'false',
                         'show': 'false',
                         'value': '1'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186298',
                         'name': 'Job',
                         'required': 'false',
                         'show': 'false',
                         'value': '20095.SA'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186299',
                         'name': 'Employee',
                         'required': 'false',
                         'show': 'false',
                         'value': '2308'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186300',
                         'name': 'BulkBatchId',
                         'required': 'false',
                         'show': 'false',
                         'value': '3855f70f-bbb6-478c-98be-c1e3a0a4aaf7'}]} from envelope ID: 6ef13fd1-3d29-468d-90fe-9534c5427fee
2025-03-31 06:09:42,507 - docusign_logger - INFO - ------------------Custom fields: {'list_custom_fields': [],
 'text_custom_fields': [{'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186291',
                         'name': 'Recipient Name',
                         'required': 'false',
                         'show': 'false',
                         'value': 'Delbert Baker'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186292',
                         'name': 'PRCo',
                         'required': 'false',
                         'show': 'false',
                         'value': '1'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186293',
                         'name': 'Job',
                         'required': 'false',
                         'show': 'false',
                         'value': '20095.SA'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186294',
                         'name': 'Employee',
                         'required': 'false',
                         'show': 'false',
                         'value': '2258'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186295',
                         'name': 'BulkBatchId',
                         'required': 'false',
                         'show': 'false',
                         'value': '3855f70f-bbb6-478c-98be-c1e3a0a4aaf7'}]} from envelope ID: 23602f33-3762-43d3-84ff-6672b37f8077
2025-03-31 06:09:42,808 - docusign_logger - INFO - ------------------Custom fields: {'list_custom_fields': [],
 'text_custom_fields': [{'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186286',
                         'name': 'Recipient Name',
                         'required': 'false',
                         'show': 'false',
                         'value': 'David Michonski'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186287',
                         'name': 'PRCo',
                         'required': 'false',
                         'show': 'false',
                         'value': '1'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186288',
                         'name': 'Job',
                         'required': 'false',
                         'show': 'false',
                         'value': '20095.SA'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186289',
                         'name': 'Employee',
                         'required': 'false',
                         'show': 'false',
                         'value': '2189'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186290',
                         'name': 'BulkBatchId',
                         'required': 'false',
                         'show': 'false',
                         'value': '3855f70f-bbb6-478c-98be-c1e3a0a4aaf7'}]} from envelope ID: 99b1ab06-8475-4590-91e6-815c6f276f48
2025-03-31 06:09:43,053 - docusign_logger - INFO - ------------------Custom fields: {'list_custom_fields': [],
 'text_custom_fields': [{'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186281',
                         'name': 'Recipient Name',
                         'required': 'false',
                         'show': 'false',
                         'value': 'Thomas Stefanelli'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186282',
                         'name': 'PRCo',
                         'required': 'false',
                         'show': 'false',
                         'value': '1'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186283',
                         'name': 'Job',
                         'required': 'false',
                         'show': 'false',
                         'value': '20095.SA'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186284',
                         'name': 'Employee',
                         'required': 'false',
                         'show': 'false',
                         'value': '2073'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11186186285',
                         'name': 'BulkBatchId',
                         'required': 'false',
                         'show': 'false',
                         'value': '3855f70f-bbb6-478c-98be-c1e3a0a4aaf7'}]} from envelope ID: 9c326ff8-ca4c-4851-86f7-3e0f3c89298a
2025-03-31 06:09:43,053 - docusign_logger - INFO - Envelope data: [{'envelope_id': 'f2cfe5d2-12a9-484e-bc03-34600e28b6ef', 'PRCo': '1', 'Job': '20095.SA', 'Employee': '2117'}, {'envelope_id': '4ae52ad5-d7fb-4b36-bd3c-a7e0de5527b1', 'PRCo': '1', 'Job': '20095.SA', 'Employee': '2262'}, {'envelope_id': '6ef13fd1-3d29-468d-90fe-9534c5427fee', 'PRCo': '1', 'Job': '20095.SA', 'Employee': '2308'}, {'envelope_id': '23602f33-3762-43d3-84ff-6672b37f8077', 'PRCo': '1', 'Job': '20095.SA', 'Employee': '2258'}, {'envelope_id': '99b1ab06-8475-4590-91e6-815c6f276f48', 'PRCo': '1', 'Job': '20095.SA', 'Employee': '2189'}, {'envelope_id': '9c326ff8-ca4c-4851-86f7-3e0f3c89298a', 'PRCo': '1', 'Job': '20095.SA', 'Employee': '2073'}], type: <class 'list'>
2025-03-31 06:09:43,053 - docusign_logger - INFO - Envelope IDs: ['9c326ff8-ca4c-4851-86f7-3e0f3c89298a', 'f2cfe5d2-12a9-484e-bc03-34600e28b6ef', '23602f33-3762-43d3-84ff-6672b37f8077', '99b1ab06-8475-4590-91e6-815c6f276f48', '4ae52ad5-d7fb-4b36-bd3c-a7e0de5527b1', '6ef13fd1-3d29-468d-90fe-9534c5427fee']
2025-03-31 06:09:55,459 - docusign_logger - INFO - Logs have been inserted into custom tracker successfully.
2025-03-31 06:09:55,506 - docusign_logger - INFO - Logging signer info on tracking table.
2025-03-31 06:10:00,396 - docusign_logger - INFO - Docusign workflow completed successfully.
