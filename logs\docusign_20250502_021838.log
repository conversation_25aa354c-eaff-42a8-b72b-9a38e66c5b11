2025-05-02 02:18:38,108 - docusign_logger - INFO - DocuSign process started
2025-05-02 02:18:38,283 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-05-02 02:18:39,603 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-05-02 02:18:42,740 - docusign_logger - INFO - Bulk send will send signature request to 74 employees.
2025-05-02 02:18:49,019 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2134}
2025-05-02 02:18:50,434 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwAAkdj3UYndSAgAAPmcWVqJ3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.KuU2oVWyJt8N7IiHXrHfvKHrkncrIwIxMjpSOZxPBKptV5Y_2nqK_ltrjHlv3Dpk-NzcQsyGQJkDwFXCwP4vlaPdqRGR0xXoP4U8Ij1Sr2WJXNIAdj9aH9dxiM5Idg-Meakzw-PVmpjRO-7lVJv_0v6PsdUG3dv_oty851ralaXBzbSxVWx42PF5qkcnawEYKebm5XJiYlF2dKk6E_5AG_vtFDywrtec-NnbJAh8GZ-wcFfbI655ra0QxEBIPDa7rph7G_EiFdgYscWleoql2aPtb6n7Tl1SaImg-zVJHKXkIkVFGvI1DjTVgAvMZ5cqToU-6MWfVMbWryGXVr5Bbg
2025-05-02 02:18:50,435 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-05-02 02:19:04,160 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.pdf', 'doc_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.doc', 'success': True, 'error': None}
2025-05-02 02:19:04,168 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-05-02 02:19:09,504 - docusign_logger - INFO - Newly Created Batch status: {'action': 'Send',
 'action_status': 'Processing',
 'batch_id': '1501f457-ccfb-4b45-8321-d721277608cf',
 'batch_name': 'Bulk Send List for Job-250565',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': '99e10c47-76f6-43a4-a16c-09c121bc0a64',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': None,
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/1501f457-ccfb-4b45-8321-d721277608cf/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '4a8d2c90-64a5-4e07-b317-112cea7b4c24',
 'mailing_list_name': 'Bulk Send List for Job-250565',
 'owner_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'queued': '1',
 'resends_remaining': 'N/A',
 'sender_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'sent': '0',
 'submitted_date': '2025-05-02T15:19:08.5070000Z'} Batch ID: 1501f457-ccfb-4b45-8321-d721277608cf
2025-05-02 02:19:09,507 - docusign_logger - INFO - Bulk send initiated with batch ID: 1501f457-ccfb-4b45-8321-d721277608cf
2025-05-02 02:19:09,513 - docusign_logger - INFO - Bulk send initiated successfully with Batch ID: 1501f457-ccfb-4b45-8321-d721277608cf. Cleaning up the temporary files.
2025-05-02 02:19:09,515 - docusign_logger - INFO - Background process started to monitor batch and set reminders. Please wait.
2025-05-02 02:19:19,522 - docusign_logger - INFO - Checking for envelopes in batch (Attempt 1/80)...
2025-05-02 02:19:20,842 - docusign_logger - INFO - Batch status: {'action': 'Send',
 'action_status': 'Complete',
 'batch_id': '1501f457-ccfb-4b45-8321-d721277608cf',
 'batch_name': 'Bulk Send List for Job-250565',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': '99e10c47-76f6-43a4-a16c-09c121bc0a64',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': '1',
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/1501f457-ccfb-4b45-8321-d721277608cf/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '4a8d2c90-64a5-4e07-b317-112cea7b4c24',
 'mailing_list_name': 'Bulk Send List for Job-250565',
 'owner_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'queued': '0',
 'resends_remaining': '3',
 'sender_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'sent': '1',
 'submitted_date': '2025-05-02T15:19:08.5070000Z'}
2025-05-02 02:19:21,181 - docusign_logger - INFO - Batch size: 1
2025-05-02 02:19:21,519 - docusign_logger - INFO - ------------------Custom fields: {'list_custom_fields': [],
 'text_custom_fields': [{'configuration_type': None,
                         'error_details': None,
                         'field_id': '5136254100',
                         'name': 'Recipient Name',
                         'required': 'false',
                         'show': 'false',
                         'value': 'Jeffri Wright'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5136254101',
                         'name': 'PRCo',
                         'required': 'false',
                         'show': 'false',
                         'value': '5'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5136254102',
                         'name': 'Job',
                         'required': 'false',
                         'show': 'false',
                         'value': '250565'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5136254103',
                         'name': 'Employee',
                         'required': 'false',
                         'show': 'false',
                         'value': '2134'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5136254104',
                         'name': 'BulkBatchId',
                         'required': 'false',
                         'show': 'false',
                         'value': '1501f457-ccfb-4b45-8321-d721277608cf'}]} from envelope ID: 92455175-7aab-4a37-b9b3-677236fda6c4
2025-05-02 02:19:21,522 - docusign_logger - INFO - Envelope data: [{'envelope_id': '92455175-7aab-4a37-b9b3-677236fda6c4', 'PRCo': '5', 'Job': '250565', 'Employee': '2134'}], type: <class 'list'>
2025-05-02 02:19:21,522 - docusign_logger - INFO - Envelope IDs: ['92455175-7aab-4a37-b9b3-677236fda6c4']
2025-05-02 02:19:23,865 - docusign_logger - INFO - Logs have been inserted into custom tracker successfully.
2025-05-02 02:19:23,891 - docusign_logger - INFO - Logging signer info on tracking table.
2025-05-02 02:19:26,611 - docusign_logger - INFO - UD fields cleaned up successfully.
2025-05-02 02:19:26,616 - docusign_logger - INFO - Docusign workflow completed successfully.
