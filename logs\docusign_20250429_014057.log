2025-04-29 01:40:57,537 - docusign_logger - INFO - DocuSign process started
2025-04-29 01:40:58,407 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-04-29 01:41:03,497 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-29 01:41:05,987 - docusign_logger - INFO - Bulk send will send signature request to 75 employees.
2025-04-29 01:41:11,354 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2654}
2025-04-29 01:41:12,556 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwAA3Ls28YbdSAgAAESAmPmG3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.pBREFwb7tLkbgpq1MebE1KBoaZwi3KzEhtX6lBW1LYJwhU7Ru4PhOOYcX-_8ZWUqHle_Ay_G5Jh7guwfcudo1Q3t0pwAKE6IsCm3wMzNzVAdUaoU9Il9HevthyYOfdMU20qPFnHWzcQQHK1lNDYFUTOZcqaBIzVpAhp1oqle37x7BH_3Iv9B3azhfpCsVU9KVqQmGPSxZlO5wRnHyBATxZP-gqYgWpxDRapzA-RqRPcGqNaIFratP50ZggrctGdRNU0RsYsfuieoKNpn9u-gT5tDgPXMvzGfytOIGojRMGXXlgUwJLgWbORsqfGnf9g8mGe-sPiMXH3XcUlaRAfxlg
2025-04-29 01:41:12,557 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-29 01:41:22,123 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-04-29 01:41:22,141 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-29 01:41:22,172 - docusign_logger - ERROR - ==================================================
2025-04-29 01:41:22,172 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-29 01:41:22,172 - docusign_logger - ERROR - Type: FileNotFoundError
2025-04-29 01:41:22,172 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-04-29 01:41:22,172 - docusign_logger - ERROR - Traceback:
2025-04-29 01:41:22,172 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-04-29 01:41:22,172 - docusign_logger - ERROR - ==================================================
2025-04-29 01:41:22,173 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
