2025-04-28 14:31:37,518 - docusign_logger - INFO - DocuSign process started
2025-04-28 14:31:37,688 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-04-28 14:31:38,876 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-28 14:31:41,739 - docusign_logger - INFO - Bulk send will send signature request to 75 employees.
2025-04-28 14:31:47,501 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2523}
2025-04-28 14:31:48,539 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwAA0h-zk4bdSAgAADrkFJyG3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.gokKqKitsEfiakiwF3ZSSXG4JFkcPzrfneSBe0Ds9Ci9X4D1AVIOLkHPEXLxEvmj9RbB-U6TEOkMqOebQaYBCoLNh4N8m6ck3N1F96lgMoCGFjqrqOwrovRxo87J18LaN4TCbQWePpS7OKKmQxRww75lIBxF_ZGXkcSoyuVCLdoPlx5Jk0vwoRNdWDe0s4BZJoppQjBADaVsgXEChTAgfJej7RkYbgsJhhZQw021JLUDB7gNEgIXHjBLe0v-7Db4cv7tAHrBo4zoeC87sU2_pxDqnN_feJ46-kg2bd38zLp62KGYID7AI6KC6S-nRjLoAUBAtbbJMXREaFYn2mpTMQ
2025-04-28 14:31:48,540 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-28 14:31:57,611 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-04-28 14:31:57,612 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-28 14:31:57,615 - docusign_logger - ERROR - ==================================================
2025-04-28 14:31:57,615 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-28 14:31:57,615 - docusign_logger - ERROR - Type: FileNotFoundError
2025-04-28 14:31:57,615 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-04-28 14:31:57,615 - docusign_logger - ERROR - Traceback:
2025-04-28 14:31:57,615 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-04-28 14:31:57,616 - docusign_logger - ERROR - ==================================================
2025-04-28 14:31:57,618 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
