2025-04-28 15:42:53,112 - docusign_logger - INFO - DocuSign process started
2025-04-28 15:42:53,251 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-04-28 15:42:54,627 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-28 15:42:57,943 - docusign_logger - INFO - Bulk send will send signature request to 75 employees.
2025-04-28 15:43:03,761 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2654}
2025-04-28 15:43:05,035 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwAApNGnnYbdSAgAAAyWCaaG3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.HwT-PLx8GqqNQftzuNq7Z-ebTGc2R-fx6-96yT6G3NAQ_d34wY8I2DJ4JuxQgMHEpNwpxeaedQAu3SlE0XZK-7u9g8sAIMYmPgUsDVE6y3l2VWT5DwDsapMgNh44BZxpjLLIUhkdpd9fL43pywlBUoAq9WGGWJrooXA0xRLK8pSZgLEC80jIxN3G3-EZl03Q3zwc9ELvN6Mg0Rjf7VSdiCINwq3lmlajuPr9wTVqE8nyukRogZJqNx-vq6XGaZvXKP6iuJ_-08FsBv4cpG_56nnH9qzoMU7z4x-bk9G-gPlBM82mQazWguJhYBEK3ZaU1gHGWjbJFEu0i3oxTve2OQ
2025-04-28 15:43:05,037 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-28 15:43:12,327 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-04-28 15:43:12,328 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-28 15:43:12,330 - docusign_logger - ERROR - ==================================================
2025-04-28 15:43:12,330 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-28 15:43:12,330 - docusign_logger - ERROR - Type: FileNotFoundError
2025-04-28 15:43:12,330 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-04-28 15:43:12,331 - docusign_logger - ERROR - Traceback:
2025-04-28 15:43:12,331 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-04-28 15:43:12,331 - docusign_logger - ERROR - ==================================================
2025-04-28 15:43:12,332 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
