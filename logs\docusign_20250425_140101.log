2025-04-25 14:01:01,639 - docusign_logger - INFO - DocuSign process started
2025-04-25 14:01:02,175 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 1, Job = 25056. 5, Username = olsen.sql.p4m
2025-04-25 14:01:05,112 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-25 14:01:35,415 - docusign_logger - ERROR - ==================================================
2025-04-25 14:01:35,415 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-25 14:01:35,415 - docusign_logger - ERROR - Type: OperationalError
2025-04-25 14:01:35,415 - docusign_logger - ERROR - Message: ('08001', '[08001] [Microsoft][ODBC Driver 17 for SQL Server]TCP Provider: The wait operation timed out.\r\n (258) (SQLDriverConnect); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online. (258)')
2025-04-25 14:01:35,415 - docusign_logger - ERROR - Traceback:
2025-04-25 14:01:35,415 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 348, in start_docusign_bulk_send_within_tkinter
    result = check_job_details(company, job)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\dbmodels.py", line 304, in check_job_details
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
pyodbc.OperationalError: ('08001', '[08001] [Microsoft][ODBC Driver 17 for SQL Server]TCP Provider: The wait operation timed out.\r\n (258) (SQLDriverConnect); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online. (258)')

2025-04-25 14:01:35,415 - docusign_logger - ERROR - ==================================================
2025-04-25 14:01:35,415 - docusign_logger - INFO - Exception occurred: <class 'pyodbc.OperationalError'>, eSignatureapi_docusign_bulksend.py, 348
