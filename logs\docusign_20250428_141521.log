2025-04-28 14:15:21,086 - docusign_logger - INFO - DocuSign process started
2025-04-28 14:15:21,270 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-04-28 14:15:22,591 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-28 14:15:25,515 - docusign_logger - INFO - Bulk send will send signature request to 75 employees.
2025-04-28 14:15:30,574 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2523}
2025-04-28 14:15:31,384 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwCAc8lskYbdSAgAgNuNzpmG3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.NuV0uhWYpeoQT7kE_zLeJCM0bA2jpUQfDPMAH-F5D-7X2JgEIFzBwSHzmzPxRPyrZWRWT7RVu8rG1g8XyoPrnqHqwgoZQqrgTed_TErzRCQiHnSVr6SQVxhxQiFzoDckODHzcDfbWBPW3xUwhjzpAZ7nOdNEg4w5co3Op-0mJBLj-6y3cbi2SioJ5vpbfh-P5z3nOdSV9MW2_f8Ds5eDtS3KsZCU8lffGKL7iKGA3kAk1VMWPOHEUqd6muqpTG5C7VEdfsWVyaAVJuu3yDjNDkK5AvMk9JMPdedYexeCSLcveno4td1EIx-EQlmPiaEXIxXxzXVKjouQCEQuTI2aBA
2025-04-28 14:15:31,385 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-28 14:15:37,334 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-04-28 14:15:37,335 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-28 14:15:37,351 - docusign_logger - ERROR - ==================================================
2025-04-28 14:15:37,352 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-28 14:15:37,352 - docusign_logger - ERROR - Type: FileNotFoundError
2025-04-28 14:15:37,352 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-04-28 14:15:37,353 - docusign_logger - ERROR - Traceback:
2025-04-28 14:15:37,353 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-04-28 14:15:37,353 - docusign_logger - ERROR - ==================================================
2025-04-28 14:15:37,356 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
