from io import BytesIO
from docx.shared import Cm
import pyodbc
import jwt
from jose import jws
from cryptography.hazmat.primitives import serialization as crypto_serialization
import time
import base64 
import requests
import os
########## Remember: we are taking Text as CustomField here because tkinter has same Text() function. So we are separating it.
from docusign_esign import RecipientViewRequest, EnvelopeDefinition, Document, Signer, SignHere, Approve, Tabs, Recipients, ApiClient, EnvelopesApi, Text as CustomField, DateSigned, CarbonCopy, InitialHere, RecipientEmailNotification
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend
import json
import jwt
from datetime import date
from docusign import create_api_client
import sys
from dbmodels import check_current_version, get_PREH_job_details, update_resend_checkbox, check_job_details, get_template_details, insert_signing_request, insert_into_custom_tracker, update_ud_fields
from docx2pdf import convert
from datetime import datetime
import ctypes
from PyPDF2 import PdfFileMerger
from tkinter.ttk import *
from tkinter import *
from tkinter import messagebox
import tkinter
from tkinter.constants import *
import shutil
from collections import Counter
import re
from utils import *
from docusign_bulksend import DocuSignBulkSender
from config import Config
from crystal_report_converter import CrystalReportConverter
from exception_logger import *
import tkinter as tk
from tkinter import ttk, messagebox
from version_control import check_version_control

logger = setup_logger()
logger.info("DocuSign process started")

status_text = None


def preview_and_select_employees(employee_details):
    # Initialize preview window
    preview_win = tk.Toplevel()
    preview_win.title("Employee Preview")
    preview_win.geometry("800x500")
    
    # Make it modal
    preview_win.transient()
    preview_win.grab_set()
    
    # Create a frame for the content
    main_frame = ttk.Frame(preview_win, padding=10)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # Add a header
    header_label = ttk.Label(
        main_frame,
        text="Please review and select employees to send documents:",
        font=("Arial", 12, "bold")
    )
    header_label.pack(pady=(0, 10), anchor=tk.W)
    
    # Count label to show selection stats
    count_label = ttk.Label(
        main_frame,
        text=f"Total Employees: {len(employee_details)} (all selected)"
    )
    count_label.pack(pady=(0, 5), anchor=tk.W)

    # Create frame to hold the treeview and scrollbar
    tree_frame = ttk.Frame(main_frame)
    tree_frame.pack(fill=tk.BOTH, expand=True)
    
    # Add scrollbar
    scrollbar = ttk.Scrollbar(tree_frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # Define columns for the treeview (removed phone column)
    columns = ('select', 'employee', 'firstName', 'lastName', 'email')
    
    # Create the treeview with columns
    tree = ttk.Treeview(
        tree_frame, 
        columns=columns,
        show='headings',
        selectmode='none',
        yscrollcommand=scrollbar.set
    )
    
    # Configure the scrollbar
    scrollbar.config(command=tree.yview)
    
    # Define column headings and widths (removed phone column)
    tree.heading('select', text='Select')
    tree.heading('employee', text='Employee #')
    tree.heading('firstName', text='First Name')
    tree.heading('lastName', text='Last Name')
    tree.heading('email', text='Email')
    
    # Configure column widths and alignment (use 'w' for left alignment)
    tree.column('select', width=60, anchor='center')
    tree.column('employee', width=100, anchor='w')
    tree.column('firstName', width=140, anchor='w')
    tree.column('lastName', width=140, anchor='w')
    tree.column('email', width=250, anchor='w')
    
    # Pack the treeview
    tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    # Dictionary to store checkbox states with employee ID as key
    checkbox_states = {}
    
    # Function to toggle checkbox state for a row
    def toggle_item(event):
        region = tree.identify_region(event.x, event.y)
        if region == "cell":
            row_id = tree.identify_row(event.y)
            if row_id:
                emp_id = tree.item(row_id, "values")[1]  # Get Employee # from values
                current_state = checkbox_states[int(emp_id)]
                # Toggle the state
                checkbox_states[int(emp_id)] = not current_state
                # Update the visual indicator (removed phone field)
                tree.item(row_id, values=(
                    "☑" if checkbox_states[int(emp_id)] else "☐",
                    emp_id,
                    tree.item(row_id, "values")[2],
                    tree.item(row_id, "values")[3],
                    tree.item(row_id, "values")[4]
                ))
                # Update count label
                selected_count = sum(1 for state in checkbox_states.values() if state)
                count_label.config(text=f"Total Employees: {len(employee_details)} ({selected_count} selected)")
    
    # Bind the toggle function to mouse click event
    tree.bind("<Button-1>", toggle_item)
    
    # Insert employee data into the treeview
    for emp in employee_details:
        emp_id = emp['Employee']
        # Default all checkboxes to checked (True)
        checkbox_states[emp_id] = True
        
        # Insert row with checkbox visual and employee data (removed phone field)
        tree.insert('', 'end', values=(
            "☑",  # Initial checkbox state (checked)
            emp_id,
            emp.get('FirstName', ''),
            emp.get('LastName', ''),
            emp.get('Email', '')
        ))
    
    # Button frame for actions
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=10)
    
    # Select/Deselect All buttons
    def select_all():
        for emp_id in checkbox_states:
            checkbox_states[emp_id] = True
        
        for item in tree.get_children():
            values = list(tree.item(item, "values"))
            values[0] = "☑"  # Set checkbox visual to checked
            tree.item(item, values=values)
        
        count_label.config(text=f"Total Employees: {len(employee_details)} (all selected)")
    
    def deselect_all():
        for emp_id in checkbox_states:
            checkbox_states[emp_id] = False
        
        for item in tree.get_children():
            values = list(tree.item(item, "values"))
            values[0] = "☐"  # Set checkbox visual to unchecked
            tree.item(item, values=values)
        
        count_label.config(text=f"Total Employees: {len(employee_details)} (0 selected)")
    
    select_all_btn = ttk.Button(button_frame, text="Select All", command=select_all)
    select_all_btn.pack(side=tk.LEFT, padx=5)
    
    deselect_all_btn = ttk.Button(button_frame, text="Deselect All", command=deselect_all)
    deselect_all_btn.pack(side=tk.LEFT, padx=5)
    
    # Result variable to store selected employees
    result = []
    
    # Function to handle confirmation
    def confirm():
        nonlocal result
        selected_ids = [emp_id for emp_id, selected in checkbox_states.items() if selected]
        
        if not selected_ids:
            messagebox.showwarning("No Selection", "Please select at least one employee.")
            return
        
        # Filter the original employee details to get only selected employees
        result = [emp for emp in employee_details if emp['Employee'] in selected_ids]
        preview_win.destroy()
    
    # Function to handle cancellation
    def cancel():
        nonlocal result
        result = None  # None result indicates explicit cancellation (different from empty selection)
        preview_win.destroy()
    
    # Add confirm and cancel buttons
    cancel_btn = ttk.Button(button_frame, text="Cancel", command=cancel)
    cancel_btn.pack(side=tk.RIGHT, padx=5)
    
    confirm_btn = ttk.Button(button_frame, text="Confirm Selection", command=confirm)
    confirm_btn.pack(side=tk.RIGHT, padx=5)
    
    # Center the window on screen
    preview_win.update_idletasks()
    width = preview_win.winfo_width()
    height = preview_win.winfo_height()
    x = (preview_win.winfo_screenwidth() // 2) - (width // 2)
    y = (preview_win.winfo_screenheight() // 2) - (height // 2)
    preview_win.geometry(f"{width}x{height}+{x}+{y}")
    
    # Wait for the window to be closed
    preview_win.wait_window()
    
    # Return the selected employees or None if cancelled
    return result


class CustomException(Exception):	
    pass

def docusign_token():
    iat = int(time.time())  # Convert to integer
    exp = iat + (3600 * 24)  # 24 hours token validity

    payload = {
        "sub": Config.DS_USER_ID,
        "iss": Config.DS_INTEGRATION_KEY,
        "iat": iat,
        "exp": exp,
        "aud": Config.DS_AUTHORIZATION_SERVER,
        "scope": "signature"
    }

     # No need to encode, Config.DS_PRIVATE_KEY_FILE is already bytes
    private_key_bytes = Config.DS_PRIVATE_KEY_FILE

    # Load the private key
    private_key = serialization.load_pem_private_key(
        private_key_bytes,
        password=None
    )

    # Convert private key to PEM format
    key = private_key.private_bytes(
        serialization.Encoding.PEM,
        serialization.PrivateFormat.PKCS8,
        serialization.NoEncryption()
    )

    # Generate JWT using PyJWT (correct way)
    jwt_token = jwt.encode(payload, key, algorithm="RS256")

    return jwt_token

def initialize_config(secret_data):
    # Database URLs
    Config.CONN_STR_VIEWPOINT = secret_data.get('VIEWPOINT_DB_URL')
    Config.CONN_STR_VPATTACHMENT = secret_data.get('VP_ATTACHMENTS_DB_URL')
    Config.CONN_STR_SMART_SIGN = secret_data.get('ESIGNATURE_DB_URL')
    
    # DocuSign configuration
    Config.DS_INTEGRATION_KEY = secret_data.get('DS_INTEGRATION_KEY')
    Config.DS_ACCOUNT_ID = secret_data.get('DS_ACCOUNT_ID')
    Config.DS_USER_ID = secret_data.get('DS_USER_ID')
    Config.DS_PRIVATE_KEY_FILE = secret_data.get('DS_PRIVATE_KEY_FILE').encode('utf-8')
    Config.DS_AUTHORIZATION_SERVER = secret_data.get('DS_AUTHORIZATION_SERVER')
    Config.DS_BASE_URL = secret_data.get('DS_BASE_URL')
    Config.DS_ACCOUNT_URL = secret_data.get('DS_ACCOUNT_URL')
    Config.DS_BRAND_ID = secret_data.get('DS_BRAND_ID')
    Config.DS_BASE_PATH = secret_data.get('DS_BASE_PATH') + '/restapi'
    logger.info(f"DocuSign configuration initialized successfully from Redis or Cache")

def create_bulk_recipient_data(job_desc):
    recipients_list = []
    for row in job_desc:
        recipient = {
            "role_name": "signer",
            "name": row['FirstName'] + ' ' + row['LastName'],
            "email": row['Email'],
            "Employee": row['Employee'],
            "FirstName": row['FirstName'],
            "LastName": row['LastName']
        }
        recipients_list.append(recipient)
    return recipients_list

def main():
    def start_docusign_bulk_send_within_tkinter():
        try:
            # Check version control to see if any update is available
            with open('version_control.json', 'r') as f:
                version_data = json.load(f)
                
            db_version = check_current_version()
            if db_version is not None and int(db_version) > int(version_data['current_version']):
                messagebox.showinfo("Vista by Viewpoint", "A new version is available. Updating the application...")
                import subprocess
                update_script_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "update_docusign.py")
                subprocess.Popen([sys.executable, update_script_path])
                master.destroy()
                return
            

            #Since we are accessing python file from batch file and accessing templates from firectory, we need to set base path using os
            base_dir = os.path.dirname(os.path.realpath(__file__))
            os.chdir(base_dir)
            master.iconbitmap(os.path.join(base_dir, "icons", 'logo.ico'))
            
            ##Cleaning required folders
            base_dir = os.path.dirname(os.path.realpath(__file__))
            os.chdir(base_dir)
            templates_directory = os.path.join(base_dir, Config.TEMPLATES_PATH)
            docusign_ready_directory = os.path.join(base_dir, Config.DOCUSIGN_Ready_Path)
            docusign_completed_directory = os.path.join(base_dir, Config.DOCUSIGN_CompletedPath)
            delete_contents_of_dir(templates_directory) 
            delete_contents_of_dir(docusign_ready_directory) 
            delete_contents_of_dir(docusign_completed_directory)

            #Getting current datetime to use it as an extension with filename
            date_time_str = datetime.now().strftime('%Y%m%d%H%M%S')

            #############################################
            args = parse_args()
            company =  str(args['Company']).strip()
            job = str(args['Job']).strip()
            username = str(args['Username']).strip()

            print('***************** Docusign bulk sendprocess will be generated for below parameters:')
            print("Company = ", company, "Job = ", job, "Username = ", username)
            logger.info(f"Docusign bulk sendprocess will be generated for below parameters: Company = {company}, Job = {job}, Username = {username}")
            pb1['value'] = 5
            master.update_idletasks()
            update_status_panel(status_text, master, datetime.now().strftime('%H:%M:%S') +": Provided input is correct.")

            ## Hardcoding the job here temporarily
            # company = 10
            # job = '12016.'


            # Get secrets and initialize config variables
            secret_data = extract_secrets_from_azure_key_vault(Config.AZURE_KEYVAULT_SECRET_VALUE)
            initialize_config(secret_data)

            result = check_job_details(company, job)
            if len(result) == 0:
                logger.error(f"No records found for this job. Please check if this job belongs to a certified payroll.")
                messagebox.showinfo("Vista by Viewpoint",  "No records found for this job. Please check if this job belongs to a certified payroll.")
                master.destroy()
                return

            employee_alternate_email = result[0]['udEmployeeAlternativeEmail'] if result[0]['udEmployeeAlternativeEmail'] else None
            beginning_employee = None
            ending_employee = None
            if result[0]['udBeginningEmployee'] and result[0]['udEndingEmployee']:   ##If there is a range of employees or a particular employee number provided with alternate email, then we will send the signature request to that range or particular employee with employee alternate email overriding employee emails if provided
                beginning_employee = int(result[0]['udBeginningEmployee'])
                ending_employee = int(result[0]['udEndingEmployee'])
                logger.info(f"BulkSend will send signature request to employee ranging from {result[0]['udBeginningEmployee']} to {result[0]['udEndingEmployee']}")
            
            elif (result[0]['udBeginningEmployee'] and not result[0]['udEndingEmployee']) or (not result[0]['udBeginningEmployee'] and result[0]['udEndingEmployee']):
                logger.error(f"Please provide beginning and ending employee numbers if you want to send to an alternate email. Otherwise, leave these fields blank.")
                messagebox.showinfo("Vista by Viewpoint",  "Please provide beginning and ending employee numbers if you want to send to an alternate email. Otherwise, leave these fields blank.")
                master.destroy()
                return
 

            employee_details = get_PREH_job_details(company, job, beginning_employee, ending_employee, employee_alternate_email)

            if len(employee_details) == 0:
                messagebox.showinfo("Vista by Viewpoint",  "No records found for this job. Please check if this job belongs to a certified payroll. Or existing batch may exist for this job.")
                master.destroy()
                return
            
            ##Sending to 1 employee
            if beginning_employee == ending_employee and len(employee_details) == 1:
                employee_details = [employee_details[0]]
                if employee_alternate_email:
                    employee_details[0]['Email'] = employee_alternate_email
                # if employee_details[0]['Email'] is None or employee_details[0]['Email'] == '':
                #     logger.error(f"Employee {employee_details[0]['Employee']} does not have an email address. Please make sure the employee has an email address.")
                #     messagebox.showinfo("Vista by Viewpoint",  f"Employee {employee_details[0]['Employee']} does not have an email address. Please make sure the employee has an email address.")
                #     master.destroy()
                #     return

            logger.info(f"Bulk send will send signature request to {len(employee_details)} employees.")
            print(f'*********** Bulk send will send signature request to {len(employee_details)} employees.')   

            updated_employee_details = preview_and_select_employees(employee_details)

            if updated_employee_details is None:
                logger.warning("Bulk send process cancelled by user.")
                messagebox.showinfo("Vista by Viewpoint", "Operation cancelled by user.")
                master.destroy()
                return

            selected_employee_ids = {item['Employee'] for item in updated_employee_details}
            employee_details = [
                emp for emp in employee_details if emp['Employee'] in selected_employee_ids
            ]
            
            response = messagebox.askyesno("Vista by Viewpoint", f"DocuSign Bulk send will send signature request to {len(employee_details)} employees. Do you want to continue?")
            if not response:
                logger.warning(f"Bulk send process terminated by user.")
                master.destroy()
                return  # Process will terminate if user selects "No"

            logger.info(f"Final: Bulk send will send signature request to {len(employee_details)} employees: {selected_employee_ids}")
            pb1['value'] = 25
            master.update_idletasks()

            # print('final employee_details: ', employee_details)
            # exit()

            # Initialize bulk sender
            token_ = docusign_token()
            post_data = {'grant_type': 'urn:ietf:params:oauth:grant-type:jwt-bearer', 'assertion': token_} 
            base_url = 'https://' + Config.DS_ACCOUNT_URL + '/oauth/token'
            r = requests.post(base_url, data=post_data)
            access_token = r.json()['access_token']
            api_client = ApiClient()
            api_client.host = Config.DS_BASE_PATH
            api_client.set_default_header('Authorization', 'Bearer ' + access_token)
            envelopes_api = EnvelopesApi(api_client)
            
            
            print('DocuSign token generated successfully: ', access_token)
            logger.info(f"DocuSign token generated successfully: {access_token}")

            pb1['value'] = 10
            master.update_idletasks()
            logger.info(f"DocuSign instance is initialized successfully. Preparing crystal report.")
            update_status_panel(status_text, master, datetime.now().strftime('%H:%M:%S') +": DocuSign instance is initialized successfully. Preparing crystal report.")
            
            
            parent_dir = os.path.dirname(base_dir)
            exe_file_path = os.path.join(parent_dir, "CrystalReportManager", "CrystalReportDemo.exe")
            report_filename = Config.CRYSTAL_REPORT_NAME
            converter = CrystalReportConverter(exe_file_path)
            parameters = {
                "Company": int(company),
                "Job": job,
                "AwardDate":  'USE_DEFAULT' if employee_details[0]['udJobAwardDate'] is None else employee_details[0]['udJobAwardDate'],
                "StartDate":  'USE_DEFAULT' if employee_details[0]['udStartDate'] is None else employee_details[0]['udStartDate'],
                "MeetingDate": 'USE_DEFAULT' if employee_details[0]['udMeetingDate'] is None else employee_details[0]['udMeetingDate'],
                "MeetingType": 'USE_DEFAULT' if employee_details[0]['udGroupOrIndividuals'] is None else employee_details[0]['udGroupOrIndividuals'],
                "RepName": 'USE_DEFAULT' if employee_details[0]['udRepresentativeName'] is None else employee_details[0]['udRepresentativeName'],
                "RepEmail": 'USE_DEFAULT' if employee_details[0]['udRepresentativeEmail'] is None else employee_details[0]['udRepresentativeEmail'],
                "BegEmp": str(employee_details[0]['Employee']),
                "EndEmp": str(employee_details[0]['Employee']),
                "EmpAltEmail": 'USE_DEFAULT' if employee_details[0]['udEmployeeAlternativeEmail'] is None else employee_details[0]['udEmployeeAlternativeEmail'],
                "DateReportingFormat": 1
            }
            result = converter.generate_report(report_filename, parameters)
            print('Crystal report generation result: ', result)
            logger.info(f"Crystal report generation result: {result}")
            if result['success']:
                pdf_filepath = result['pdf_path']
                doc_filepath = result['doc_path']
            else:
                logger.error(f"Crystal report generation failed. Please check the logs of Crystal Report Manager.")
                messagebox.showinfo("Vista by Viewpoint",  "Crystal report generation failed. Please check the logs of Crystal Report Manager.")
                master.destroy()
                return
            
            pb1['value'] = 30
            master.update_idletasks()
            logger.info(f"Crystal report generated successfully. Preparing to send documents to DocuSign.")
            update_status_panel(status_text, master, datetime.now().strftime('%H:%M:%S') +": Crystal report generated successfully. Preparing to send documents to DocuSign.")

            docusign_ready_dir = os.path.join(base_dir, Config.DOCUSIGN_Ready_Path)
            if not os.path.exists(docusign_ready_dir):
                os.makedirs(docusign_ready_dir)
            pdf_destination = os.path.join(docusign_ready_dir, os.path.basename(pdf_filepath))
            doc_destination = os.path.join(docusign_ready_dir, os.path.basename(doc_filepath))
            shutil.copy(pdf_filepath, pdf_destination)
            shutil.copy(doc_filepath, doc_destination)


            # ################## Use this piece of the code to send bulk email to selected personal email addresses.
            # test_email_manager = TestEmailManager() #This class returns some dummy emails.
            # dummy_employees = []

            # for i in range(min(len(employee_details), 6)):  # Limit to max 6 employees
            #     dummy_employees.append({
            #         "role_name": "signer",
            #         "name": employee_details[i]['FirstName'] + ' ' + employee_details[i]['LastName'],
            #         "Employee": employee_details[i]['Employee'],
            #         "FirstName": employee_details[i]['FirstName'],
            #         "LastName": employee_details[i]['LastName'],
            #         "Email": test_email_manager.get_next_email(),
            #         "PRCo": employee_details[i]['PRCo'],
            #         "Job": str(employee_details[i]['Job']).replace('.', '').replace(' ', '').strip(),
            #         "Phone": employee_details[i]['Phone']
            #     })
            # ################## ########################################################################

            final_recipients = []
            #for recipient in dummy_employees:       ########### Use this only for testing limiting to send bulk email only to personal email addresses.
            for recipient in employee_details:  
                recipient_with_doc = {
                    "role_name": "signer",
                    "name": recipient.get('FirstName', '') + ' ' + recipient.get('LastName', ''),
                    "email": recipient["Email"],
                    "phone": recipient["Phone"],
                    "document_path": pdf_destination,
                    "document_id": "1",
                    "document_name": recipient['Job'] + '-' + recipient.get('FirstName', '') + ' ' + recipient.get('LastName', '') + '.pdf',
                    "PRCo": recipient['PRCo'],
                    "PRGroup": recipient['PRGroup'],
                    "Job": str(recipient['Job']).replace('.', '').replace(' ', '').strip(),
                    "Employee": recipient['Employee'],
                    'FirstName': recipient.get('FirstName', ''),
                    'LastName': recipient.get('LastName', '')
                }
                
                final_recipients.append(recipient_with_doc)          

            print('final_recipients: ', final_recipients)
            
            pb1['value'] = 50
            master.update_idletasks()
            bulk_sender = DocuSignBulkSender(envelopes_api)
            batch_id = bulk_sender.send_bulk_documents(final_recipients)
            print(f"Bulk send initiated with batch ID: {batch_id}")
            logger.info(f"Bulk send initiated with batch ID: {batch_id}")
            
            
            pb1['value'] = 70
            master.update_idletasks()
            logger.info(f"Bulk send initiated successfully with Batch ID: {batch_id}. Cleaning up the temporary files.")
            update_status_panel(status_text, master, datetime.now().strftime('%H:%M:%S') +": Bulk send initiated successfully with Batch ID: " + batch_id + ". Cleaning up the temporary files.")


            pb1['value'] = 75
            master.update_idletasks()
            logger.info(f"Background process started to monitor batch and set reminders. Please wait.")
            update_status_panel(status_text, master, datetime.now().strftime('%H:%M:%S') +": Background process started to monitor batch and set reminders. Please wait.")
            envelope_data = bulk_sender.set_reminder_expiry(access_token, batch_id)    ## This function has been threaded in the docusign_bulksend.py file. It will run in the background.
            
            updated_employee_details = bulk_sender.add_batch_info_into_custom_tracker(final_recipients, envelope_data)
            for emp in updated_employee_details:
                insert_into_custom_tracker(company, emp, batch_id, job, username)

            pb1['value'] = 95
            master.update_idletasks()
            logger.info(f"Logs have been inserted into custom tracker successfully.")
            update_status_panel(status_text, master, datetime.now().strftime('%H:%M:%S') +": Logs have been inserted into custom tracker successfully.")

            #insert_envelope_ids_into_tracking(company, employee_details[0]['PRCo'], employee_details[0]['PRGroup'],       envelope_ids, batch_id,  job, username)

            # update_status_panel(status_text, master, datetime.now().strftime('%H:%M:%S') +": Inserting unsigned copy into VPAttachment database.")
            # insert_attachment_vpattachment_database_azure(pdf_destination, os.path.basename(pdf_destination), company, job, username)

            logger.info(f"Logging signer info on tracking table.")
            update_status_panel(status_text, master, datetime.now().strftime('%H:%M:%S') +": Logging signer info on tracking table.")
            template_name = Config.CRYSTAL_REPORT_NAME + ".rpt"
            template_info = get_template_details(template_name)
            insert_signing_request("", "", "", "", 0, batch_id, 'N', datetime.now().strftime('%Y-%m-%d %H:%M:%S'), None, int(company),  job, None, int(template_info[0]['TemplateId']), 'docusign' )

            for f in os.listdir(os.path.join(base_dir, Config.DOCUSIGN_Ready_Path)):
                try:
                    os.remove(os.path.join(base_dir, Config.DOCUSIGN_Ready_Path, f))
                except:
                    pass
            for f in os.listdir(os.path.join(base_dir, Config.TEMPLATES_PATH)):
                try:
                    os.remove(os.path.join(base_dir, Config.TEMPLATES_PATH, f))  
                except:
                    pass
            for f in os.listdir(os.path.join(base_dir, Config.DOCUSIGN_CompletedPath)):
                try:
                    os.remove(os.path.join(base_dir, Config.DOCUSIGN_CompletedPath, f)) 
                except:
                    pass
            ############### Cleaning phase completed

            update_resend_checkbox()

            ## Cleaning up ud fields:
            fields_to_clean = ['udMeetingDate', 'udRepresentativeName', 'udRepresentativeEmail', 'udBeginningEmployee', 'udEndingEmployee', 'udEmployeeAlternativeEmail']
            update_ud_fields(company, job, fields_to_clean)
            logger.info(f"UD fields cleaned up successfully.")

            pb1['value'] = 100
            master.update_idletasks()
            logger.info(f"Docusign workflow completed successfully.")
            messagebox.showinfo("Vista by Viewpoint",  "Docusign workflow completed successfully.")
            master.destroy()

        except Exception as e:
            log_exception(logger, e, "DocuSign Main Process")
            error_details = traceback.format_exc()
            print("Exception occurred:", error_details)
        
            exc_type, exc_obj, exc_tb = sys.exc_info()
            fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            print(exc_type, fname, exc_tb.tb_lineno)
            logger.info(f"Exception occurred: {exc_type}, {fname}, {exc_tb.tb_lineno}")   
            messagebox.showinfo("Vista by Viewpoint",  "DocuSign exception occured. Details: " + str(exc_type) + "\r\n" + str(fname) + "\r\n" + str(exc_tb.tb_lineno) + '\r\n' + str(e))
            master.destroy()
            return

    master = tkinter.Tk()
    # Set the width and height of our root window.
    master_window_height = 300
    master.geometry("900x300")
    master.title('Vista by Viewpoint: Docusign Workflow Started')

    pb1 = Progressbar(master, orient=HORIZONTAL, length=100, mode='determinate')
    pb1.pack(pady = 10)


    # Create label
    l = Label(master, text = "DocuSign In Progress")
    l.config(font =("Arial", 14))
    l.pack()

    status_label = Label(master,text = "Status").place(x = 50, y=master_window_height-180) 
    status_text = Text(master, height = 8, width = 65)
    status_text.config(font =("Arial", 9))
    status_text.place(x=110, y=master_window_height-180)
    update_status_panel(status_text, master, datetime.now().strftime('%H:%M:%S') +": DocuSign workflow started.")

    master.after(2, start_docusign_bulk_send_within_tkinter)
    master.mainloop()

if __name__ == "__main__":
    main()
