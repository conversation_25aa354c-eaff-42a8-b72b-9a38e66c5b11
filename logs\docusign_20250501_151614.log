2025-05-01 15:16:14,490 - docusign_logger - INFO - DocuSign process started
2025-05-01 15:16:14,663 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-05-01 15:16:15,711 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-05-01 15:16:18,679 - docusign_logger - INFO - Bulk send will send signature request to 74 employees.
2025-05-01 15:16:22,126 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2685}
2025-05-01 15:16:22,919 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwAAFzFs9YjdSAgAAH_1zf2I3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.ZkOG83ORyUe3IRfCdpbR5r5CiZL-PCSghjsoa3C_KtC6_BlvB3ou7HoRvrDajh-0wKQyLXNUl8IK28vsNwwJy1DdUgOopoE0JvGY4h9bppN0K23lumgYNNcKWXc2lwUo8Sr36LxdHGuXGWCEykVbl8LKc4mrWIyYXt6gRPU09uuvQpXIRA4_tW6M-C5LvuyakQ8bBkNX4a2iL2txf23jXUuwnEHn2BWO53qAs53EkRJ_13GTyD4q1637knRGulF4Tob52DjZaiCaN4vbUjxlTwAMsEM8yZUDMg3sjkecSFzYd1wIMW8xGIaxQ6f79e13eM5RnGEH18OXAEp9tlDX6g
2025-05-01 15:16:22,921 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-05-01 15:16:30,662 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-05-01 15:16:30,662 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-05-01 15:16:30,693 - docusign_logger - ERROR - ==================================================
2025-05-01 15:16:30,694 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-05-01 15:16:30,694 - docusign_logger - ERROR - Type: FileNotFoundError
2025-05-01 15:16:30,694 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-05-01 15:16:30,694 - docusign_logger - ERROR - Traceback:
2025-05-01 15:16:30,694 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-05-01 15:16:30,694 - docusign_logger - ERROR - ==================================================
2025-05-01 15:16:30,694 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
