2025-05-13 16:21:19,884 - docusign_logger - INFO - DocuSign process started
2025-05-13 16:21:20,073 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 24140. 5, Username = olsen.sql.p4m
2025-05-13 16:21:21,531 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-05-13 16:21:24,704 - docusign_logger - INFO - Bulk send will send signature request to 74 employees.
2025-05-13 16:21:36,860 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {573}
2025-05-13 16:21:37,816 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwCArquGbJLdSAgAgBZw6HSS3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.gX7c3Kbspf6dndkDXfkiL0GoG7k__BhHm64gb_CWZ8nOAaeSwVnEjlpDQsMOqPvcJBcr4HqYtwJxuXsWF33xfOVCfM9Qxy-c2W5c2dwnCUKoQzbSBrmQFvPFfE1cwKmy3OtZEVksXOeDuK3tZiW8dT_VKfoirK8s7h4rRihWFAgtrNNYQbyDEhLni4TX_AP9RtvAs-mCNmFojxroSML7e6ctGxe_-8VHjT6gWnRmApQLc6cXMYxbFL5fe-EB9rPYIFDx1ASeHF9e1Rh0lKVXJpxib-L1H2it9IzNnZx09TwTGdXnweIzHxHbcrOomxkESv3p86lNaZ6S0oJ92jb_Lw
2025-05-13 16:21:37,821 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-05-13 16:21:49,840 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-05-13 16:21:49,847 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-05-13 16:21:49,860 - docusign_logger - ERROR - ==================================================
2025-05-13 16:21:49,860 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-05-13 16:21:49,860 - docusign_logger - ERROR - Type: FileNotFoundError
2025-05-13 16:21:49,861 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-05-13 16:21:49,861 - docusign_logger - ERROR - Traceback:
2025-05-13 16:21:49,861 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-05-13 16:21:49,861 - docusign_logger - ERROR - ==================================================
2025-05-13 16:21:49,862 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
