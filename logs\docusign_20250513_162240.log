2025-05-13 16:22:40,861 - docusign_logger - INFO - DocuSign process started
2025-05-13 16:22:41,028 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 24140. 5, Username = olsen.sql.p4m
2025-05-13 16:22:41,985 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-05-13 16:22:44,779 - docusign_logger - INFO - Bulk send will send signature request to 74 employees.
2025-05-13 16:22:49,620 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {573}
2025-05-13 16:22:50,559 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwAAmS6ybJLdSAgAAAHzE3WS3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.CP6q-R3nW2X7ujUOkOZHfLjGxw5WV_qFI4gqGHRwNgNttslod_8H4E8uy__4uEDZfFozqp0OuDhQTQ0iV9zBiwqe_8KlsC_Rdn5ni69fkimnt_x1-tPPHJmNAZdXmLUokMuCSH_9DRLMCI3LnWXvLFz9WEUcDoXum_xCIJqRvmj_ify1kNNAQ-9G0wflzQ0taRiVNiWDfwVuMGn1iVbBg2KF7_UKlXPOJ9QQCPRQ89oLar-xfGi7efyFvf5UhMq-nvO2qGARHAI6xJw-tljqOK61xbGGwnlQGDOusGjViBplqjUuv7gFeCmGhWuak7WE0-skb94Tf_ZItpNqfKXzuw
2025-05-13 16:22:50,560 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-05-13 16:23:04,542 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.pdf', 'doc_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.doc', 'success': True, 'error': None}
2025-05-13 16:23:04,583 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-05-13 16:23:09,770 - docusign_logger - INFO - Newly Created Batch status: {'action': 'Send',
 'action_status': 'Processing',
 'batch_id': '4319485a-62f4-494d-ae4a-949fc9fd70c0',
 'batch_name': 'Bulk Send List for Job-241405',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': '97f25d04-6298-4245-a457-0923e6df66e9',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': None,
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/4319485a-62f4-494d-ae4a-949fc9fd70c0/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '7f57bd8c-41cb-4a4b-ac1f-16561e599e2d',
 'mailing_list_name': 'Bulk Send List for Job-241405',
 'owner_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'queued': '1',
 'resends_remaining': 'N/A',
 'sender_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'sent': '0',
 'submitted_date': '2025-05-14T05:23:08.7470000Z'} Batch ID: 4319485a-62f4-494d-ae4a-949fc9fd70c0
2025-05-13 16:23:09,772 - docusign_logger - INFO - Bulk send initiated with batch ID: 4319485a-62f4-494d-ae4a-949fc9fd70c0
2025-05-13 16:23:09,773 - docusign_logger - INFO - Bulk send initiated successfully with Batch ID: 4319485a-62f4-494d-ae4a-949fc9fd70c0. Cleaning up the temporary files.
2025-05-13 16:23:09,781 - docusign_logger - INFO - Background process started to monitor batch and set reminders. Please wait.
2025-05-13 16:23:19,795 - docusign_logger - INFO - Checking for envelopes in batch (Attempt 1/80)...
2025-05-13 16:23:20,440 - docusign_logger - INFO - Batch status: {'action': 'Send',
 'action_status': 'Complete',
 'batch_id': '4319485a-62f4-494d-ae4a-949fc9fd70c0',
 'batch_name': 'Bulk Send List for Job-241405',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': '97f25d04-6298-4245-a457-0923e6df66e9',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': '1',
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/4319485a-62f4-494d-ae4a-949fc9fd70c0/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '7f57bd8c-41cb-4a4b-ac1f-16561e599e2d',
 'mailing_list_name': 'Bulk Send List for Job-241405',
 'owner_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'queued': '0',
 'resends_remaining': '3',
 'sender_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'sent': '1',
 'submitted_date': '2025-05-14T05:23:08.7470000Z'}
2025-05-13 16:23:20,702 - docusign_logger - INFO - Batch size: 1
2025-05-13 16:23:20,895 - docusign_logger - INFO - ------------------Custom fields: {'list_custom_fields': [],
 'text_custom_fields': [{'configuration_type': None,
                         'error_details': None,
                         'field_id': '5155148032',
                         'name': 'Recipient Name',
                         'required': 'false',
                         'show': 'false',
                         'value': 'Edward J.  Priemon'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5155148033',
                         'name': 'PRCo',
                         'required': 'false',
                         'show': 'false',
                         'value': '5'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5155148034',
                         'name': 'Job',
                         'required': 'false',
                         'show': 'false',
                         'value': '241405'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5155148035',
                         'name': 'Employee',
                         'required': 'false',
                         'show': 'false',
                         'value': '573'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5155148036',
                         'name': 'BulkBatchId',
                         'required': 'false',
                         'show': 'false',
                         'value': '4319485a-62f4-494d-ae4a-949fc9fd70c0'}]} from envelope ID: 95e39454-15c2-4191-bdfa-1abbb167c39e
2025-05-13 16:23:20,900 - docusign_logger - INFO - Envelope data: [{'envelope_id': '95e39454-15c2-4191-bdfa-1abbb167c39e', 'PRCo': '5', 'Job': '241405', 'Employee': '573'}], type: <class 'list'>
2025-05-13 16:23:20,900 - docusign_logger - INFO - Envelope IDs: ['95e39454-15c2-4191-bdfa-1abbb167c39e']
2025-05-13 16:23:22,096 - docusign_logger - INFO - Logs have been inserted into custom tracker successfully.
2025-05-13 16:23:22,099 - docusign_logger - INFO - Logging signer info on tracking table.
2025-05-13 16:23:23,881 - docusign_logger - INFO - UD fields cleaned up successfully.
2025-05-13 16:23:23,886 - docusign_logger - INFO - Docusign workflow completed successfully.
