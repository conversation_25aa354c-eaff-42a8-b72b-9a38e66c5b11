2025-04-29 02:12:18,464 - docusign_logger - INFO - DocuSign process started
2025-04-29 02:12:18,619 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-04-29 02:12:19,960 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-29 02:12:23,473 - docusign_logger - INFO - Bulk send will send signature request to 75 employees.
2025-04-29 02:12:29,887 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2134}
2025-04-29 02:12:30,982 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwAA6xuW9YbdSAgAAFPg9_2G3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.d2KtA6V37iXImxxejApg9AnwyPzLHpmtc14qwkeArcRzzHDcIAo0tbTQNcST6x2TRv-RXoStlQmLhhwJqPO6ZTv26M2eZ6Wzf7IpaovQ6Av0Mvrin6fYWLNVortVC8dzviFShBtfhHlQ1AwRDmtM4xJCNqVn6WF9dMwAJT3Fsc0Tteah7s_Zu_Oglix0JYiSZDjLryNVlee5F-oD4rNAGmkoBr_pzVwnzsvr1wD1Rvqu4lDPuFpFkhnYLcYe_u9aB3BZsD28ZcFmzKqOUb_AaifrOA4D0xO3DNG5s4cB6K4xfkAg-Jt3RKfrVj3iBMcKQ5O2hI0bWivxcRVri8WfyQ
2025-04-29 02:12:30,989 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-29 02:12:43,092 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.pdf', 'doc_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.doc', 'success': True, 'error': None}
2025-04-29 02:12:43,092 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-29 02:12:47,219 - docusign_logger - ERROR - ==================================================
2025-04-29 02:12:47,219 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-29 02:12:47,219 - docusign_logger - ERROR - Type: ApiException
2025-04-29 02:12:47,219 - docusign_logger - ERROR - Message: (400)
Reason: Bad Request
Trace-Token: 78779c9a-4a3e-4606-9a25-a832edce3a56
Timestamp: Tue, 29 Apr 2025 08:12:46 GMT
HTTP response headers: HTTPHeaderDict({'Cache-Control': 'no-cache', 'Content-Length': '299', 'Content-Type': 'application/json; charset=utf-8', 'Vary': 'Origin', 'X-Content-Type-Options': 'nosniff', 'X-RateLimit-Reset': '1745917200', 'X-RateLimit-Remaining': '2996', 'X-RateLimit-Limit': '3000', 'X-BurstLimit-Remaining': '496', 'X-BurstLimit-Limit': '500', 'X-DocuSign-TraceToken': '78779c9a-4a3e-4606-9a25-a832edce3a56', 'X-DocuSign-Node': 'DA103FE85', 'Date': 'Tue, 29 Apr 2025 08:12:46 GMT', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'})
HTTP response body: b'{"envelopeOrTemplateId":"22a87c54-bb2f-47ae-a0a9-80c1c268a18a","batchId":"00000000-0000-0000-0000-000000000000","batchSize":"1","totalQueued":"0","queueLimit":"2000","errors":["BULK_SEND_ENVELOPE_HAS_NO_TABS"],"errorDetails":["Bulk sending copy contains tabs, but the specified envelope does not."]}'

2025-04-29 02:12:47,220 - docusign_logger - ERROR - Traceback:
2025-04-29 02:12:47,220 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 525, in start_docusign_bulk_send_within_tkinter
    batch_id = bulk_sender.send_bulk_documents(final_recipients)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\docusign_bulksend.py", line 281, in send_bulk_documents
    batch = bulk_envelopes_api.create_bulk_send_request(
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\site-packages\docusign_esign\apis\bulk_envelopes_api.py", line 175, in create_bulk_send_request
    (data) = self.create_bulk_send_request_with_http_info(account_id, bulk_send_list_id, **kwargs)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\site-packages\docusign_esign\apis\bulk_envelopes_api.py", line 249, in create_bulk_send_request_with_http_info
    return self.api_client.call_api(resource_path, 'POST',
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\site-packages\docusign_esign\client\api_client.py", line 355, in call_api
    return self.__call_api(resource_path, method,
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\site-packages\docusign_esign\client\api_client.py", line 169, in __call_api
    response_data = self.request(method, url,
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\site-packages\docusign_esign\client\api_client.py", line 398, in request
    return self.rest_client.POST(url,
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\site-packages\docusign_esign\client\api_response.py", line 288, in POST
    return self.request("POST", url,
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\site-packages\docusign_esign\client\api_response.py", line 247, in request
    raise ApiException(http_resp=r)
docusign_esign.client.api_exception.ApiException: (400)
Reason: Bad Request
Trace-Token: 78779c9a-4a3e-4606-9a25-a832edce3a56
Timestamp: Tue, 29 Apr 2025 08:12:46 GMT
HTTP response headers: HTTPHeaderDict({'Cache-Control': 'no-cache', 'Content-Length': '299', 'Content-Type': 'application/json; charset=utf-8', 'Vary': 'Origin', 'X-Content-Type-Options': 'nosniff', 'X-RateLimit-Reset': '1745917200', 'X-RateLimit-Remaining': '2996', 'X-RateLimit-Limit': '3000', 'X-BurstLimit-Remaining': '496', 'X-BurstLimit-Limit': '500', 'X-DocuSign-TraceToken': '78779c9a-4a3e-4606-9a25-a832edce3a56', 'X-DocuSign-Node': 'DA103FE85', 'Date': 'Tue, 29 Apr 2025 08:12:46 GMT', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'})
HTTP response body: b'{"envelopeOrTemplateId":"22a87c54-bb2f-47ae-a0a9-80c1c268a18a","batchId":"00000000-0000-0000-0000-000000000000","batchSize":"1","totalQueued":"0","queueLimit":"2000","errors":["BULK_SEND_ENVELOPE_HAS_NO_TABS"],"errorDetails":["Bulk sending copy contains tabs, but the specified envelope does not."]}'


2025-04-29 02:12:47,220 - docusign_logger - ERROR - ==================================================
2025-04-29 02:12:47,223 - docusign_logger - INFO - Exception occurred: <class 'docusign_esign.client.api_exception.ApiException'>, eSignatureapi_docusign_bulksend.py, 525
