2025-05-20 14:06:24,577 - docusign_logger - INFO - DocuSign process started
2025-05-20 14:06:24,786 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 24140. 5, Username = olsen.sql.p4m
2025-05-20 14:06:25,949 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-05-20 14:06:28,728 - docusign_logger - INFO - Bulk send will send signature request to 76 employees.
2025-05-20 14:06:38,803 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2904}
2025-05-20 14:06:39,747 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwCAMcfU2ZfdSAgAgJmLNuKX3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.ggDMHoR8mZRE-HvRAnSEMIUNrmsXwodiOPDiqmrRO77z6gyXDXRV4sHlaa-D5ou1B9dPPet4TxMBK45GJ5jAsrTGM9dARX-kMrQ22__QdxTtnb73j0_SnoWjAdlhrG6j0kViHeV1_kZVXw74myuN_zwW-H4ujqxm8RfiuAgLjOwdEUmATDJ6GlFADeVxkOzgk6xSzCYr73qTcBqPewnCeUarvAVVLIgBej7tUqIVkFAFqoceNY9n_EuOjumDyoBQLKfsT9CogpBmdRv2-5KsZOxg4ikOgfG__pFpzu1Tcpuv_6t-Oem0azCINWylPgVF67NTYtynrRIKovDAi_cTZA
2025-05-20 14:06:39,752 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-05-20 14:06:48,040 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': False, 'error': 'Failed to generate the report. Please check the Crystal report logs.'}
2025-05-20 14:06:48,040 - docusign_logger - ERROR - Crystal report generation failed. Please check the logs of Crystal Report Manager.
