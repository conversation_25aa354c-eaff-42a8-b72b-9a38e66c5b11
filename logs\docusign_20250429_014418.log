2025-04-29 01:44:18,770 - docusign_logger - INFO - DocuSign process started
2025-04-29 01:44:18,928 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-04-29 01:44:20,114 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-29 01:44:22,794 - docusign_logger - INFO - Bulk send will send signature request to 75 employees.
2025-04-29 01:44:27,125 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2527}
2025-04-29 01:44:28,031 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwCAf_aq8YbdSAgAgOe6DPqG3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.r4TByZMIAXIDESB1BgwAQc7djR5uLilXtOFFNfMcnbKKiCEdFk0TR7Ag4lgwKUaBhqTkzslhbE6O8NGNZG-I3C1yq_evvUk-tSeE8a0XLYVefOHYvAPfzpCzSytc79z9Q2hklhFUpkOazgbPQYgjybOaFnb827GFtBTnKYBJoYp6HphK0KQ0S74SYHi-ay0n6nS5x_K8tIdyHJijopJYNrV9Rd5RO11ZKtRswpzTlT6owaEsLG-qCkRELtOlHaOPbuZkhaGmXYkzqdLaQvVZVok8HVLmy1-aPLwIa-YHa6NgPXTIvgbg5c280dh5f3RNc9LB5e3bVRpf55nB0iCJNw
2025-04-29 01:44:28,035 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-29 01:44:33,956 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-04-29 01:44:33,957 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-29 01:44:33,961 - docusign_logger - ERROR - ==================================================
2025-04-29 01:44:33,961 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-29 01:44:33,962 - docusign_logger - ERROR - Type: FileNotFoundError
2025-04-29 01:44:33,962 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-04-29 01:44:33,962 - docusign_logger - ERROR - Traceback:
2025-04-29 01:44:33,962 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-04-29 01:44:33,963 - docusign_logger - ERROR - ==================================================
2025-04-29 01:44:33,965 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
