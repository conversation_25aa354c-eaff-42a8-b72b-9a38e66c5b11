class Config:
    # Default static values
    CLIENT_ID = 16
    DEFAULT_CC_NAME = 'DocuSign Admin'
    DEFAULT_CC_EMAIL = '<EMAIL>'
    DOCUSIGN_Ready_Path = 'Docusignready'
    EDITED_TEMPLATES_PATH = 'EditedTemplates'
    TEMPLATES_PATH = 'Templates'
    DOCUSIGN_CompletedPath = 'Docusigncompleted'


    # Azure and Redis Configuration
    TENANT_ID = "08bc5b30-0a1f-4755-a522-64f5326319b5"  ##EarleCo Tenant ID
    CLIENT_APPLICATION_ID = "1e7913e9-210f-471b-ad47-9c71f999c530"  # Id of this app registration: tec-vista-docusign-app
    CLIENT_APPLICATION_SECRET = "****************************************"
    AZURE_KEYVAULT_SECRET_VALUE = 'EARLECO-PROD'
    AZURE_KEY_VAULT_URL = "https://tec-vista-app-kv.vault.azure.net/"
    REDIS_HOST = "tec-vista-app.redis.cache.windows.net"
    REDIS_PORT = "6380"
    REDIS_ACCESS_KEY = "EBt5ynjwbZDmOJZtX3aqvPNPJKOSh3MyYAzCaCNByhk="

    # Database Connection Strings (initialized as None)
    CONN_STR_VIEWPOINT = None
    CONN_STR_VPATTACHMENT = None
    CONN_STR_SMART_SIGN = None

    # DocuSign Configuration (initialized as None)
    DS_INTEGRATION_KEY = None
    DS_ACCOUNT_ID = None
    DS_USER_ID = None
    DS_PRIVATE_KEY_FILE = None
    DS_AUTHORIZATION_SERVER = None
    DS_BASE_URL = None
    DS_ACCOUNT_URL = None
    DS_BRAND_ID = None
    DS_BASE_PATH = None
    DEFAULT_REMINDER_FIRST_DAY = 1
    DEFAULT_REMINDER_FREQUENCY = 1
    DEFAULT_ENVELOPE_EXPIRY = 30
    DEFAULT_WARNING_BEFORE_EXPIRY = 3
    POLLING_PERIOD = 14

    ## Crystal Report Name and Parameters
    CRYSTAL_REPORT_NAME = "EAC MW212"
    CRYSTAL_REPORT_EAC_MW212_QUERY_PARAM_Shift = 1
    CRYSTAL_REPORT_EAC_MW212_QUERY_PARAM_PRGroup = 1

    ## Version control filename
    VERSION_CONTROL_FILENAME = 'version_control.json'