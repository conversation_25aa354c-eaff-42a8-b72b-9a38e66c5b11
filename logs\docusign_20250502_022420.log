2025-05-02 02:24:20,796 - docusign_logger - INFO - DocuSign process started
2025-05-02 02:24:20,955 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-05-02 02:24:22,440 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-05-02 02:24:25,581 - docusign_logger - INFO - BulkSend will send signature request to employee ranging from 1 to 3000
2025-05-02 02:24:26,162 - docusign_logger - INFO - Bulk send will send signature request to 20 employees.
2025-05-02 02:24:34,151 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2527}
2025-05-02 02:24:35,313 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwCAY3vFUondSAgAgMs_J1uJ3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.Cwhew-EF5VfFA_Gfa14_0n8drOVQVb1LqM0Q5e42h_nObvc9KhgQccSrP9O_YyyBm5lTBF4PVSj04ag7mfZFFo34cyhmXPejsXkfdg1wN1sZ37C0lECiAFFSiKLmyyYpVIgP88GhvHmbUAVoWIG_B6NN6ZLlBhmnGFIkN6RC0hZ0aa0RQYhkYLSTADlFtHXrfV_UdcnjuhQ39HwsargW78XhzkQYjio2UbwuqBC8vYq38QHYp3ARZDhqU3mQbYFwuLfGZnrVGEG2ZXwOw3T0DfDgjN9_W70H7CB217SBNqHnBESYm8vzr7ET7vV9KQHwLXEY-0Bs4p-lUHz11gQ46g
2025-05-02 02:24:35,314 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-05-02 02:24:41,815 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.pdf', 'doc_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.doc', 'success': True, 'error': None}
2025-05-02 02:24:41,816 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-05-02 02:24:47,539 - docusign_logger - INFO - Newly Created Batch status: {'action': 'Send',
 'action_status': 'Processing',
 'batch_id': '2d4bb9f0-dbc2-4163-b7ff-4f2f7dfec01b',
 'batch_name': 'Bulk Send List for Job-250565',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': '4162300e-e300-4fda-a8fc-90b2b3248647',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': None,
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/2d4bb9f0-dbc2-4163-b7ff-4f2f7dfec01b/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': 'd03ed324-68eb-41eb-aa44-7f3bceb68e62',
 'mailing_list_name': 'Bulk Send List for Job-250565',
 'owner_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'queued': '1',
 'resends_remaining': 'N/A',
 'sender_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'sent': '0',
 'submitted_date': '2025-05-02T15:24:46.5800000Z'} Batch ID: 2d4bb9f0-dbc2-4163-b7ff-4f2f7dfec01b
2025-05-02 02:24:47,540 - docusign_logger - INFO - Bulk send initiated with batch ID: 2d4bb9f0-dbc2-4163-b7ff-4f2f7dfec01b
2025-05-02 02:24:47,541 - docusign_logger - INFO - Bulk send initiated successfully with Batch ID: 2d4bb9f0-dbc2-4163-b7ff-4f2f7dfec01b. Cleaning up the temporary files.
2025-05-02 02:24:47,548 - docusign_logger - INFO - Background process started to monitor batch and set reminders. Please wait.
2025-05-02 02:24:57,556 - docusign_logger - INFO - Checking for envelopes in batch (Attempt 1/80)...
2025-05-02 02:24:58,675 - docusign_logger - INFO - Batch status: {'action': 'Send',
 'action_status': 'Complete',
 'batch_id': '2d4bb9f0-dbc2-4163-b7ff-4f2f7dfec01b',
 'batch_name': 'Bulk Send List for Job-250565',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': '4162300e-e300-4fda-a8fc-90b2b3248647',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': '1',
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/2d4bb9f0-dbc2-4163-b7ff-4f2f7dfec01b/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': 'd03ed324-68eb-41eb-aa44-7f3bceb68e62',
 'mailing_list_name': 'Bulk Send List for Job-250565',
 'owner_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'queued': '0',
 'resends_remaining': '3',
 'sender_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'sent': '1',
 'submitted_date': '2025-05-02T15:24:46.5800000Z'}
2025-05-02 02:24:59,306 - docusign_logger - INFO - Batch size: 1
2025-05-02 02:25:00,078 - docusign_logger - INFO - ------------------Custom fields: {'list_custom_fields': [],
 'text_custom_fields': [{'configuration_type': None,
                         'error_details': None,
                         'field_id': '5136255100',
                         'name': 'Recipient Name',
                         'required': 'false',
                         'show': 'false',
                         'value': 'Megan Betts'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5136255101',
                         'name': 'PRCo',
                         'required': 'false',
                         'show': 'false',
                         'value': '5'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5136255102',
                         'name': 'Job',
                         'required': 'false',
                         'show': 'false',
                         'value': '250565'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5136255103',
                         'name': 'Employee',
                         'required': 'false',
                         'show': 'false',
                         'value': '2527'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5136255104',
                         'name': 'BulkBatchId',
                         'required': 'false',
                         'show': 'false',
                         'value': '2d4bb9f0-dbc2-4163-b7ff-4f2f7dfec01b'}]} from envelope ID: c842d633-f848-4b45-adad-68ee00d785d9
2025-05-02 02:25:00,081 - docusign_logger - INFO - Envelope data: [{'envelope_id': 'c842d633-f848-4b45-adad-68ee00d785d9', 'PRCo': '5', 'Job': '250565', 'Employee': '2527'}], type: <class 'list'>
2025-05-02 02:25:00,081 - docusign_logger - INFO - Envelope IDs: ['c842d633-f848-4b45-adad-68ee00d785d9']
2025-05-02 02:25:04,496 - docusign_logger - INFO - Logs have been inserted into custom tracker successfully.
2025-05-02 02:25:04,504 - docusign_logger - INFO - Logging signer info on tracking table.
2025-05-02 02:25:07,401 - docusign_logger - INFO - UD fields cleaned up successfully.
2025-05-02 02:25:07,404 - docusign_logger - INFO - Docusign workflow completed successfully.
