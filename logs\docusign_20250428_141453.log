2025-04-28 14:14:53,490 - docusign_logger - INFO - DocuSign process started
2025-04-28 14:14:54,177 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-04-28 14:14:55,456 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-28 14:14:58,295 - docusign_logger - INFO - Bulk send will send signature request to 75 employees.
2025-04-28 14:15:02,717 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2527}
2025-04-28 14:15:03,744 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwCA_RhckYbdSAgAgGXdvZmG3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.HwGG_Cj8Zj2ZQw8MrDGelYs-rtKpqaZby8o4Fk_gGKCeOHmcOty17t7Bk5xjXlM4nrgYwb9q381Ea6d8Ut_K4HH7ykIT1AzaK7zq1xnETpaEJ0XNNzGWpuFxhu7YxBbDIn5rknhU2RBIxYpcKGussiUnAZVK4JD0WaJN5WrpmdHKL3F6x8GCyfV9Tj5ApRh3emz7nj0L8xKNXMUEhR9T7ajjOIaMGYb_28vXtNnT0Q8M8IMYOVYRzfA_y5_vWDxiwxjaz8OiUoTQaRJyvTjfwWePS2iljuHZm78rvmTPhKz_hzAJwOYLbzQXD_CJuugNlFuqcCDOUKx7Pd1wT9wfRQ
2025-04-28 14:15:03,745 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-28 14:15:10,986 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-04-28 14:15:10,987 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-28 14:15:11,039 - docusign_logger - ERROR - ==================================================
2025-04-28 14:15:11,039 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-28 14:15:11,039 - docusign_logger - ERROR - Type: FileNotFoundError
2025-04-28 14:15:11,040 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-04-28 14:15:11,040 - docusign_logger - ERROR - Traceback:
2025-04-28 14:15:11,040 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-04-28 14:15:11,041 - docusign_logger - ERROR - ==================================================
2025-04-28 14:15:11,044 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
