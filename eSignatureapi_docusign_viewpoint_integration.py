from io import BytesIO
from docx.shared import Cm
from docxtpl import DocxTemplate, InlineImage
import pyodbc
import jwt
from jose import jws
from cryptography.hazmat.primitives import serialization as crypto_serialization
import time
import base64 
import requests
import os
########## Remember: we are taking Text as CustomField here because tkin<PERSON> has same Text() function. So we are separating it.
from docusign_esign import RecipientViewRequest, EnvelopeDefinition, Document, Signer, SignHere, Tabs, Recipients, ApiClient, EnvelopesApi, Text as CustomField, DateSigned, CarbonCopy, InitialHere, RecipientEmailNotification
from docusign_utils import initialize_docusign_api, generate_ticket_number_from_inbox_folder_workorder_number
import json
import jwt
from datetime import date
import sys
from dbmodels import parse_args, replace_none_with_empty_str, update_udfields_textbox_value, GetDocusignCC<PERSON>erson, update_edit_template_textbox_value, download_template, load_ticket_number_lookup_table, get_pm_subcontract_pmsl_table, get_template_details, get_SMWorkOrderScope_Fields, cleanup_folders, insert_signing_request
from docx2pdf import convert
from datetime import datetime
import ctypes
from attachment_utils import GetAttachmentsFromBlob, insert_attachment_vpattachment_database, insert_attachment_vpattachment_database_azure
from template_queries import get_context_material_order_template, get_context_service_work_order_template, get_context_subcontract_template, get_context_site_conditions_order_template, get_context_professional_service_template
import PyPDF2
from tkinter.ttk import *
from tkinter import *
from tkinter import messagebox
import tkinter
from tkinter.constants import *
import shutil
from config import *
from collections import Counter
import re
from build_documents import build_documents, update_status_panel, manage_third_signer_field, manage_stamp_field_textfields
from docx import Document as Document_

status_text = None

class CustomException(Exception):	
    pass

def from_template_azure_blob(template_path, template_filename, company, workorder, scope, scope_begin, scope_end, show_work_date_range, attachment_list, status_text, master, folders_api):
    if not os.path.exists(template_path):
        download_template(CLIENT_ID, template_path)
    return from_template(template_path, template_filename, company, workorder, scope, scope_begin, scope_end, show_work_date_range, attachment_list, status_text, master, folders_api)
    ###Testing my approach time
    #return from_template2(template_path, template_filename, sl_number, company, project, attachment_list)

def from_template(template_path, template_filename, company, workorder, scope, scope_begin, scope_end, show_work_date_range, attachment_list, status_text, master, folders_api):
    item_count = generate_ticket_number_from_inbox_folder_workorder_number(workorder, folders_api)
    if item_count is None:
        item_count = 1
        #messagebox.showinfo("Vista by Viewpoint",  "You must create a folder in DocuSign inbox for Work Order: " + str(workorder) + ". Login to DocuSign portal: https://apps.docusign.com/send/documents and create a Work Order folder now.")

    final_docx, final_merged_pdf, ticket_number, service_date = build_documents(company, workorder, scope, scope_begin, scope_end, show_work_date_range, template_filename, template_path, attachment_list, status_text, master, item_count)
    update_status_panel(status_text, master, "***Document has been built successfully.")
    return final_docx, final_merged_pdf, ticket_number, service_date


def docusign_token(company):
    iat = time.time()
    exp = iat+(3600*24)
    payload = {
        "sub": GET_DOCUSIGN_USER_ID(company),
        "iss": DOCU_APP_INTEGRATION_KEY, #ds_client_id
        "iat": iat, # session start_time
        "exp": exp, # session end_time
        "aud": ACCOUNT_URL,
        "scope":"signature"
    }
    base_dir = os.path.dirname(os.path.realpath(__file__))
    os.chdir(base_dir)
    with open(os.path.join(os.getcwd(), PRIVATE_KEY_FILENAME), "rb") as key_file:
        private_key = crypto_serialization.load_pem_private_key(key_file.read(), password=None)
        key = private_key.private_bytes(crypto_serialization.Encoding.PEM, crypto_serialization.PrivateFormat.PKCS8, crypto_serialization.NoEncryption())
        jwt_token = jws.sign(payload, key, algorithm='RS256')
    return jwt_token

def create_jwt_grant_token(company):
    token = docusign_token(company)
    return token

def worker(args):
    """
    1. Create the envelope request object
    2. Send the envelope
    """
    # 1. Create the envelope request object
    envelope_definition = make_envelope(args)
    api_client = ApiClient()
    api_client.host = DOCUSIGN_URL + '/restapi'
    api_client.set_default_header('Authorization', 'Bearer ' + args["access_token"])

    #api_client = create_api_client(base_path=args["base_path"], access_token=args["access_token"])
    # 2. call Envelopes::create API method
    # Exceptions will be caught by the calling function
    envelopes_api = EnvelopesApi(api_client)
    results = envelopes_api.create_envelope(account_id=DOCU_API_ACCOUNT_ID, envelope_definition=envelope_definition)
    envelope_id = results.envelope_id
    return {"envelope_id": envelope_id, "access_token": args["access_token"]}

def create_stamp_fields(page, recipient_ids):
    base = 392
    height = 12
    x_ = "73"
    y_="-5"
    f1 = CustomField(document_id="3", page_number=str(page), anchor_string="Bill To:", anchor_units="pixels", anchor_x_offset=x_, anchor_y_offset=y_, tab_label="SharedField1", tab_id="SharedField1", share_to_recipients=recipient_ids, required="false", height="10", width="170", locked="false")
    
    f2 = CustomField(document_id="3", page_number=str(page), anchor_string="UWI:", anchor_units="pixels", anchor_x_offset=x_, anchor_y_offset=y_, tab_label="SharedField2", tab_id="SharedField2", share_to_recipients=recipient_ids, required="false", height="10", width="170", locked="false")
    
    f3 = CustomField(document_id="3", page_number=str(page), anchor_string="Well Name:", anchor_units="pixels", anchor_x_offset=x_, anchor_y_offset=y_, tab_label="SharedField3", tab_id="SharedField3", share_to_recipients=recipient_ids, required="false", height="10", width="170", locked="false")

    f4 = CustomField(document_id="3", page_number=str(page), anchor_string="AFE:", anchor_units="pixels", anchor_x_offset=x_, anchor_y_offset=y_, tab_label="SharedField4", tab_id="SharedField4", share_to_recipients=recipient_ids, required="false", height="10", width="170", locked="false")

    f5 = CustomField(document_id="3", page_number=str(page), anchor_string="Service Date:", anchor_units="pixels", anchor_x_offset=x_, anchor_y_offset=y_, tab_label="SharedField5", tab_id="SharedField5", share_to_recipients=recipient_ids, required="false", height="10", width="170", locked="false")

    f6 = CustomField(document_id="3", page_number=str(page), anchor_string="Approver Name:", anchor_units="pixels", anchor_x_offset=x_, anchor_y_offset=y_, tab_label="SharedField6", tab_id="SharedField6", share_to_recipients=recipient_ids, required="false", height="10", width="170", locked="false")

    f7 = CustomField(document_id="3", page_number=str(page), anchor_string="Email Invoice To:", anchor_units="pixels", anchor_x_offset=x_, anchor_y_offset=y_, tab_label="SharedField7", tab_id="SharedField7", share_to_recipients=recipient_ids, required="false", height="10", width="170", locked="false")

    f8 = CustomField(document_id="3", page_number=str(page), anchor_string="PO#:", anchor_units="pixels", anchor_x_offset=x_, anchor_y_offset=y_, tab_label="SharedField8", tab_id="SharedField8", share_to_recipients=recipient_ids, required="false", height="10", width="170", locked="false")

    f9 = CustomField(document_id="3", page_number=str(page), anchor_string="GL#:", anchor_units="pixels", anchor_x_offset=x_, anchor_y_offset=y_, tab_label="SharedField9", tab_id="SharedField9", share_to_recipients=recipient_ids, required="false", height="10", width="170", locked="false")

    f10 = CustomField(document_id="3", page_number=str(page), anchor_string="Cost Object:", anchor_units="pixels", anchor_x_offset=x_, anchor_y_offset=y_, tab_label="SharedField10", tab_id="SharedField10", share_to_recipients=recipient_ids, required="false", height="10", width="170", locked="false")

    f11 = CustomField(document_id="3", page_number=str(page), anchor_string="Work Order#:", anchor_units="pixels", anchor_x_offset=x_, anchor_y_offset=y_, tab_label="SharedField11", tab_id="SharedField11", share_to_recipients=recipient_ids, required="false", height="10", width="170", locked="false")

    f12 = CustomField(document_id="3", page_number=str(page), anchor_string="Approver Name/Code:", anchor_units="pixels", anchor_x_offset=str(int(x_)+23), anchor_y_offset=y_, tab_label="SharedField12", tab_id="SharedField12", share_to_recipients=recipient_ids, required="false", height="10", width="144", locked="false")

    f13 = CustomField(document_id="3", page_number=str(page), anchor_string="Cost Centre:", anchor_units="pixels", anchor_x_offset=x_, anchor_y_offset=y_, tab_label="SharedField13", tab_id="SharedField13", share_to_recipients=recipient_ids, required="false", height="10", width="170", locked="false")

    f14 = CustomField(document_id="3", page_number=str(page), anchor_string="Major:", anchor_units="pixels", anchor_x_offset=x_, anchor_y_offset=y_, tab_label="SharedField14", tab_id="SharedField14", share_to_recipients=recipient_ids, required="false", height="10", width="170", locked="false")

    f15 = CustomField(document_id="3", page_number=str(page), anchor_string="Minor:", anchor_units="pixels", anchor_x_offset=x_, anchor_y_offset=y_, tab_label="SharedField15", tab_id="SharedField15", share_to_recipients=recipient_ids, required="false", height="10", width="170", locked="false")

    return [f1, f2, f3, f4, f5, f6, f7, f8, f9, f10, f11, f12, f13, f14, f15]

def create_manager_signature():
    ## 2023-10-30: Manager is now called customer 2 as of new change request by Bravo. 
    sign_here1 = SignHere(
        anchor_string="/sn3/",
        anchor_units="pixels",
        anchor_y_offset="-14",
        anchor_x_offset="20"
    )

    initial_1 = InitialHere(anchor_string="Customer 2 Initials",
        anchor_units="pixels",
        anchor_y_offset="-14",
        anchor_x_offset="1"
    )
    name_field = CustomField(
        anchor_string="/cfn3/", anchor_units="pixels",
        anchor_y_offset="-18", anchor_x_offset="11",
        font="arial", font_size="size11", required="true",
        bold="true", width="170", value="",
        locked="false", tab_id="legal_name",
        tab_label="Customer 2 Name"
    )
    title_field = CustomField(
        anchor_string="/cft3/", anchor_units="pixels",
        anchor_y_offset="-18", anchor_x_offset="11",
        font="arial", font_size="size11", required="true",
        bold="true", width="170", value="",
        locked="false", tab_id="legal_title",
        tab_label="Customer 2 Title"
    )
    date_field = DateSigned(
        anchor_string="Date 3", anchor_units="pixels",
        anchor_y_offset="-18", anchor_x_offset="-25",
        font="arial", font_size="size10",
        bold="true", width="150"
    )
    return sign_here1, initial_1, name_field, title_field, date_field

def create_subcontractor_signature():
    sign_here1 = SignHere(
        anchor_string="/sn2/",
        anchor_units="pixels",
        anchor_y_offset="-14",
        anchor_x_offset="20"
    )

    initial_1 = InitialHere(anchor_string="Customer Initials",
        anchor_units="pixels",
        anchor_y_offset="-14",
        anchor_x_offset="1")

    name_field = CustomField(
        anchor_string="/cfn2/", anchor_units="pixels",
        anchor_y_offset="-18", anchor_x_offset="11",
        font="arial", font_size="size11", required="true",
        bold="true", width="170", value="",
        locked="false", tab_id="legal_name",
        tab_label="Customer Name")
    
    title_field = CustomField(
        anchor_string="/cft2/", anchor_units="pixels",
        anchor_y_offset="-18", anchor_x_offset="11",
        font="arial", font_size="size11", required="true",
        bold="true", width="170", value="",
        locked="false", tab_id="legal_title",
        tab_label="Customer Title")
    
    date_field = DateSigned(
        anchor_string="Date 2", anchor_units="pixels",
        anchor_y_offset="-18", anchor_x_offset="-25",
        font="arial", font_size="size10",
        bold="true", width="150")
    
    return sign_here1, initial_1, name_field, title_field, date_field

def create_internal_signature():
    sign_here1 = SignHere(
        anchor_string="/sn1/",
        anchor_units="pixels",
        anchor_y_offset="-14",
        anchor_x_offset="20"
    )

    # stamp_here1 = SignHere(
    #     anchor_string="Stamp 1",
    #     anchor_units="pixels",
    #     anchor_y_offset="0",
    #     anchor_x_offset="1",
    #     stamp_type = "stamp",
    #     optional = "true",
    #     scale_value = "40"
    # )

    initial_1 = InitialHere(anchor_string="Area / Project Initials",
        anchor_units="pixels",
        anchor_y_offset="-16",
        anchor_x_offset="1")

    name_field = CustomField(
        anchor_string="/cfn1/", anchor_units="pixels",   #We dont need internal name as it is coming from template already, so making the field "InternalName_",  a name that is not available in the template
        anchor_y_offset="-18", anchor_x_offset="11",
        font="arial", font_size="size11", required="true",
        bold="true", width="170", value="",
        locked="false", tab_id="legal_name",
        tab_label="Area / Project Manager Name")
    title_field = CustomField(
        anchor_string="/cft1/", anchor_units="pixels",    #We dont need internal title as it is coming from template already, so making the field "InternalTitle_",  a name that is not available in the template
        anchor_y_offset="-18", anchor_x_offset="11",
        font="arial", font_size="size11", required="true",
        bold="true", width="170", value="",
        locked="false", tab_id="legal_title",
        tab_label="Area / Project Manager Title")
    date_field = DateSigned(
        anchor_string="Date 1", anchor_units="pixels",
        anchor_y_offset="-18", anchor_x_offset="-25",
        font="arial", font_size="size10",
        bold="true", width="150")

    return sign_here1, initial_1, name_field, title_field, date_field

def create_shared_social_id_field(lastPage):
    social_id_field = CustomField(
        document_id="3",
        page_number=str(lastPage),
        anchor_string="Customer Stamp",  # You need to specify where in the document this will appear
        anchor_units="pixels",
        anchor_x_offset="30",  # Replace with the actual x offset
        anchor_y_offset="-20",  # Replace with the actual y offset
        tab_label="SocialID",  # Unique label for the shared field
        required="false",
        shared="true",
        height="10",
        width="170",
        locked="false"  # The field must be unlocked to be editable by subsequent signers
    )
    return social_id_field

def make_envelope(args):
    """
    Creates envelope
    Document 1: An HTML document.
    Document 2: A Word .docx document.
    Document 3: A PDF document.
    DocuSign will convert all of the documents to the PDF format.
    The recipients" field tags are placed using <b>anchor</b> strings.
    """

    base_dir = os.path.dirname(os.path.realpath(__file__))
    os.chdir(base_dir)

    with open(args['filepath'], "rb") as file:
        doc3_pdf_bytes = file.read()
    doc3_b64 = base64.b64encode(doc3_pdf_bytes).decode("ascii")

    pdfReader = PyPDF2.PdfFileReader(args['filepath'])
    lastPage = pdfReader.numPages

    try:
        docusign_email_page_filename_show = str(os.path.basename(args['filepath'])).split('.')[0]
        docusign_email_page_filename_show = docusign_email_page_filename_show.replace('_unsigned', '')
    except:
        docusign_email_page_filename_show = 'Template'

    document3 = Document(  # create the DocuSign document object
        document_base64=doc3_b64,
        name = docusign_email_page_filename_show,  # can be different from actual file name
        file_extension = "pdf",  # many different document types are accepted
        document_id = "3"  # a label used to reference the doc
    )
    
    # Add the recipients to the envelope object
    signer_list = []
    data = args['data']
    updated_signers = []
    if len(data) == 3:  #There is a manager in the signature request who should be at routing order 1
        has_manager = True
        updated_signers.append({'Signer': data[0]['Signer'], 'RoutingOrder': "1", 'EmailSubject': args['subject_line'], 'EmailBody': 'Please review and sign as appropriate.'})  #Bringing manager into position 1
        updated_signers.append({'Signer': data[1]['Signer'], 'RoutingOrder': "2", 'EmailSubject': args['subject_line'], 'EmailBody': 'Please click the link above to review and sign the referenced billing summary document. Once the billing summary document has been fully executed, you will receive a copy of the fully executed document. If you have any questions, please contact the Project Manager.'})  #Bringing subcontractor into position 2
        updated_signers.append({'Signer': data[2]['Signer'], 'RoutingOrder': "3", 'EmailSubject': args['subject_line'], 'EmailBody': 'Please click the link above to review and sign the referenced billing summary document. Once the billing summary document has been fully executed, you will receive a copy of the fully executed document. If you have any questions, please contact the Project Manager.'})  #Bringing internal / PM / originator into position 3
    else:
        has_manager = False
        updated_signers.append({'Signer': data[0]['Signer'], 'RoutingOrder': "1", 'EmailSubject': args['subject_line'], 'EmailBody': 'Please click the link above to review and sign the referenced billing summary document. Once the billing summary document has been fully executed, you will receive a copy of the fully executed document. If you have any questions, please contact the Project Manager.'})  #Bringing subcontractor into position 1
        updated_signers.append({'Signer': data[1]['Signer'], 'RoutingOrder': "2", 'EmailSubject': args['subject_line'], 'EmailBody': 'Please click the link above to review and sign the referenced billing summary document. Once the billing summary document has been fully executed, you will receive a copy of the fully executed document. If you have any questions, please contact the Project Manager.'})  #Bringing internal / PM / originator into position 2

    for row in updated_signers:
        signer1 = Signer(
            email=row['Signer'],
            name=str(row['Signer']).split('@')[0],
            recipient_id=row["RoutingOrder"],
            routing_order=row["RoutingOrder"],
            email_notification = RecipientEmailNotification(email_subject = row['EmailSubject'], email_body = row['EmailBody'])
        )
        signer_list.append(signer1)

    sign_here_list = []
    stamp_here_list = []
    initials_here_list = []
    text_legal_1 = []
    text_legal_2 = []
    text_legal_3 = []

    ## Creating the shared fields..
    # shared_fields = []
    # f1, f2, f3, f4, f5, f6, f7, f8, f9, f10, f11, f12, f13, f14, f15 = create_stamp_fields(lastPage)
    # shared_fields.append(f1)
    # shared_fields.append(f2)
    # shared_fields.append(f3)
    # shared_fields.append(f4)
    # shared_fields.append(f5)
    # shared_fields.append(f6)
    # shared_fields.append(f7)
    # shared_fields.append(f8)
    # shared_fields.append(f9)
    # shared_fields.append(f10)
    # shared_fields.append(f11)
    # shared_fields.append(f12)
    # shared_fields.append(f13)
    # shared_fields.append(f14)
    # shared_fields.append(f15)

    i = 0
    ##Only subcontractor / customer will have stamp feature. Internal / Area / Project Manager will not have any stamp
    recipient_ids = ""
    if has_manager:
        ####Inserting internal / area / project manager. NO STAMP for him
        sign_here, initial, name_field, title_field, date_field = create_internal_signature()
        initials_here_list.append(initial)
        text_legal_1.append(name_field)
        text_legal_1.append(title_field)
        text_legal_1.append(date_field)
        sign_here_list.append(sign_here)
        #stamp_here_list.append(stamp_here1)

        ####Inserting subcontractor / customer. YES STAMP
        sign_here, initial, name_field, title_field, date_field = create_subcontractor_signature()
        initials_here_list.append(initial)
        text_legal_2.append(name_field)
        text_legal_2.append(title_field)
        text_legal_2.append(date_field)
        sign_here_list.append(sign_here)
        #stamp_here_list.append(stamp_here1)

        ####Inserting manager if provided
        sign_here, initial, name_field, title_field, date_field = create_manager_signature()
        initials_here_list.append(initial)
        text_legal_3.append(name_field)
        text_legal_3.append(title_field)
        text_legal_3.append(date_field)
        sign_here_list.append(sign_here)
        recipient_ids = "2,3"
    else:
        ####Inserting internal / area / project manager
        sign_here, initial, name_field, title_field, date_field = create_internal_signature()
        initials_here_list.append(initial)
        text_legal_1.append(name_field)
        text_legal_1.append(title_field)
        text_legal_1.append(date_field)
        sign_here_list.append(sign_here)
        #stamp_here_list.append(stamp_here1)

        ####Inserting subcontractor / customer. YES STAMP
        sign_here, initial, name_field, title_field, date_field = create_subcontractor_signature()
        initials_here_list.append(initial)
        text_legal_2.append(name_field)
        text_legal_2.append(title_field)
        text_legal_2.append(date_field)
        sign_here_list.append(sign_here)
        #stamp_here_list.append(stamp_here1)
        recipient_ids = "2"

    shared_fields = create_stamp_fields(lastPage, recipient_ids)
    for i in range(0, len(data)):
        if i==0:    ####Inserting internal / area / project manager. NO STAMP for him
            signer_list[i].tabs = Tabs(sign_here_tabs=[sign_here_list[i]], initial_here_tabs=[initials_here_list[i]], text_tabs=[text_legal_1[0], text_legal_1[1]], 
                                                                                                                                date_signed_tabs=[text_legal_1[2]])
        
        if i==1:    ####Inserting subcontractor / customer. YES STAMP
            signer_list[i].tabs = Tabs(sign_here_tabs=[sign_here_list[i]], initial_here_tabs=[initials_here_list[i]], text_tabs=[text_legal_2[0], text_legal_2[1], 
                                                                                                                                 shared_fields[0], shared_fields[1], shared_fields[2], shared_fields[3],
                                                                                                                                 shared_fields[4], shared_fields[5], shared_fields[6], shared_fields[7],
                                                                                                                                 shared_fields[8], shared_fields[9], shared_fields[10], shared_fields[11],
                                                                                                                                 shared_fields[12], shared_fields[13], shared_fields[14]],
                                                                                                                                date_signed_tabs=[text_legal_2[2]])
        if i==2:    ####Inserting manager / Customer 2 if provided
            signer_list[i].tabs = Tabs(sign_here_tabs=[sign_here_list[i]], initial_here_tabs=[initials_here_list[i]], text_tabs=[text_legal_3[0], text_legal_3[1]],
                                                                                                                                date_signed_tabs=[text_legal_3[2]])
    
    # for i in range(len(signer_list)):
    #     if i == 0:    ###Inserting internal / area / project manager. NO STAMP for him
    #         signer_list[i].tabs = Tabs(sign_here_tabs=[sign_here_list[i]], initial_here_tabs=[initials_here_list[i]], text_tabs=[text_legal_1[0], text_legal_1[1]], 
    #                                                                                                                                 date_signed_tabs=[text_legal_1[2]])

    #     if i == 1:    ####Inserting subcontractor / customer. YES STAMP
    #         shared_fields_signer_2 = create_stamp_fields(lastPage, "2")
    #         signer_list[i].tabs = Tabs(sign_here_tabs=[sign_here_list[i]], initial_here_tabs=[initials_here_list[i]], text_tabs=[text_legal_2[0], text_legal_2[1]] + shared_fields_signer_2, date_signed_tabs=[text_legal_2[2]])

    #     if i == 2:   ####Inserting manager / Customer 2 if provided
    #         shared_fields_signer_3 = create_stamp_fields(lastPage, "3")
    #         # Set tabs for the third signer including shared fields
    #         signer_list[i].tabs = Tabs(sign_here_tabs=[sign_here_list[i]], initial_here_tabs=[initials_here_list[i]], text_tabs=[text_legal_3[0], text_legal_3[1]] + shared_fields_signer_3, date_signed_tabs=[text_legal_3[2]])


    # shared_tab = create_shared_social_id_field(lastPage)
    # for i in range(0, len(data)):
    #     # Add the Social ID field to the shared fields for Signer 2 and Signer 3
    #     if i == 1 or i == 2:  # Assuming Signer 2 is at index 1 and Signer 3 is at index 2
    #         signer_list[i].tabs.text_tabs.append(shared_tab)


    cc_emails = args['cc_email']
    email_cc = cc_emails.split(';')
    email_cc = list(set(email_cc))
    cc_list = []
    k = 1
    for item in email_cc:
        cc1 = CarbonCopy(
            email = item,
            name = item.split('@')[0],
            recipient_id = len(data) + k,   ##The 'cc' person will get maximum routing order
            routing_order= len(data) + k
        )
        cc_list.append(cc1)
        k += 1


    recipients = Recipients(signers=signer_list, carbon_copies=cc_list)
    
    # create the envelope definition
    env = EnvelopeDefinition(email_subject=args['subject_line'], email_blurb = 'Thank you.', brand_id='9e506e46-c381-489b-a7d0-39867fefef17')
    env.documents =  [document3]
    #recipients = Recipients(signers=signer_objects, carbon_copies=[])
    env.recipients = recipients

    # Request that the envelope be sent by setting |status| to "sent".
    # To request that the envelope be created as a draft, set to "created"
    env.status = args["status"]

    return env

def check_template_name(template_name):
    #First check if the template exists. If exists, then simply return the template id. Otherwise, insert the template and then return the templated id
    result = get_template_details(template_name)
    print('************* ', template_name)
    if len(result) > 0:     #means, the template information is available in the database
        return int(result[0]['TemplateId']), str(result[0]['TemplateName'])
    else:
        # insert_template_info(template_name)
        # result = get_template_details(template_name)
        raise CustomException("Template not found. Please insert this template into database.")

def send_reminder_expiry(envelope_id, first_reminder_day, reminder_frequency, days_before_expirity, days_before_expire_warning, access_token):
    set_reminder_day = "true"
    first_reminder_day = int(first_reminder_day)
    reminder_frequency = int(reminder_frequency)

    set_expiry_day = "true"
    days_before_expirity = int(days_before_expirity)
    days_before_expire_warning = int(days_before_expire_warning)
    
    #print("{\n  \"useAccountDefaults\": \"false\",\n  \"reminders\": {\n    \"reminderEnabled\": \"true\",\n    \"reminderDelay\": \"1\",\n    \"reminderFrequency\": \"1\"\n  },\n  \"expirations\": {\n    \"expireEnabled\": \"true\",\n    \"expireAfter\": \"4\",\n    \"expireWarn\": \"3\"\n  }\n}")
    print("checked envelope_id ", envelope_id, set_reminder_day, first_reminder_day, reminder_frequency, set_expiry_day, days_before_expirity, days_before_expire_warning)
    
    url = DOCUSIGN_URL + "/restapi/v2.1/accounts/" + str(DOCU_API_ACCOUNT_ID) + "/envelopes/" + str(envelope_id) + "/notification"
    payload = "{\n  \"useAccountDefaults\": \"false\",\n  \"reminders\": {\n    \"reminderEnabled\": \"" + set_reminder_day + "\",\n    \"reminderDelay\": \"" + str(first_reminder_day) + "\",\n    \"reminderFrequency\": \"" + str(reminder_frequency) + "\"\n  },\n  \"expirations\": {\n    \"expireEnabled\": \"" + str(set_expiry_day) + "\",\n    \"expireAfter\": \"" + str(days_before_expirity) + "\",\n    \"expireWarn\": \"" + str(days_before_expire_warning) + "\"\n  }\n}"
    headers = {
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + access_token
    }
    #print(payload)
    response = requests.request("PUT", url, headers=headers, data=payload)
    #messagebox.showinfo("Vista by Viewpoint",  response.text)
    print(response.text)

def main():

    def start_docusign_within_tkinter():
        try:
            #Since we are accessing python file from batch file and accessing templates from firectory, we need to set base path using os
            base_dir = os.path.dirname(os.path.realpath(__file__))
            os.chdir(base_dir)
            parent_dir = os.path.abspath(os.path.join(base_dir, os.pardir))
            master.iconbitmap(os.path.join(parent_dir, 'Kernel', 'bravotarget_favicon.ico'))

            #############################################
            args = parse_args()
            button_params = get_SMWorkOrderScope_Fields(args['Company'], args['WorkOrder'], args['Seq'])
            if len(button_params) == 0:
                messagebox.showinfo("Vista by Viewpoint",  "Cannot find any data for this workorder and scope in SMWorkOrderScope.")
                master.destroy()
                return
                        
            company =  str(args['Company']).strip()
            workorder =  str(args['WorkOrder']).strip()
            #scope =  str(args['Seq']).strip()  ### *** We are not using this scope anymore. We will use the udWorkOrderScope field. If empty, we will set it to 0 so that the stored procedure will consider all the scope

            smworkscope = replace_none_with_empty_str(button_params[0])
            scope_ = smworkscope['udWorkOrderScope']
            scope_begin_ = smworkscope['udWorkOrderScopeBegin']
            scope_end_ = smworkscope['udWorkOrderScopeEnd']
            if scope_ == '':
                scope = 0
            else:
                scope = int(smworkscope['udWorkOrderScope'])

            if scope_begin_ == '':
                scope_begin = '1950-01-01 00:00:00'
            else:
                scope_begin = str(scope_begin_)

            if scope_end_ == '':
                scope_end = datetime.today().strftime('%Y-%m-%d %H:%M:%S')
            else:
                scope_end = str(scope_end_)

            show_work_date_range = False
            if scope_begin_ != '' and scope_end_ != '':
                show_work_date_range = True

            print('Parameters passed: ', smworkscope)
            
            template_name =  str(smworkscope['udTemplateNameDropdown']).strip()
            email1 =  str(smworkscope['udCreateAndSendEmail1']).strip()     #For BRAVO field ticket, Internal will get signature request first, Then subcontracor and then 3rd / optional signer
            routing1 =  "1"
            email2 =  str(smworkscope['udCreateAndSendEmail2']).strip()
            routing2 =  "2"
            email3 =  str(smworkscope['udCreateAndSendEmail3']).strip()
            routing3 =  "3"
            attachment_list =  ''
            CCEmails =  str(smworkscope['udEmailCC']).strip()
            smworkorderscopeid = int(smworkscope['SMWorkOrderScopeID'])     #It will be used to insert into VPAttachments database.SMWorkOrderScopeID is used in the KeyField
            edited_template_name =  str(smworkscope['udEditedTemplate']).strip()

            res = GetDocusignCCPerson()
            if len(res) > 0:
                cc_email_1 = res[0]['EMail']
                cc_name_1 = res[0]['EMail'].split('@')[0]
            else:
                cc_name_1 = DEFAULT_CC_NAME
                cc_email_1 = DEFAULT_CC_EMAIL

            if CCEmails == '' or CCEmails is None:
                cc_email = cc_email_1
                cc_name = cc_name_1
            else:
                tmp_emails = re.findall("[0-9a-zA-z]+@[0-9a-zA-z]+\.[0-9a-zA-z]+", CCEmails)
                CCEmails = ';'.join(tmp_emails)
                cc_email = CCEmails + ';' + cc_email_1
                cc_name = ''

            if int(company) in EHSP_COMPANIES and template_name != EHSP_TEMPLATE_NAME:
                messagebox.showinfo("Vista by Viewpoint",  f"You must use template '{EHSP_TEMPLATE_NAME}' for company {company}.")
                master.destroy()
                return
            
            print('** Docusign process will be generated for below parameters. Template:', template_name, 'Company', company, 'Work Order', workorder, 'Scope', scope, 'Email 1', email1, 'Email 2', email2, 'Email 3', email3, 'Scope Begin', scope_begin, 'Scope End', scope_end, 'CC Email=', cc_email)

            if template_name == '':
                messagebox.showinfo("Vista by Viewpoint",  "You must select a template first.")
                master.destroy()
                return

            if email1 == '' or email2 == '':
                messagebox.showinfo("Vista by Viewpoint",  "Please provide subcontractor and internal email addresses.")
                master.destroy()
                return

            update_status_panel(status_text, master, "***Provided parameters are correct. Starting DocuSign integration.")

            #We may have 2 or 3 signers
            signer_list_with_order = []
            if email1 != '' and routing1 != '':
                signer_list_with_order.append({'Signer':email1, 'RoutingOrder':routing1})
            if email2 != '' and routing2 != '':
                signer_list_with_order.append({'Signer':email2, 'RoutingOrder':routing2})
            if email3 != '' and routing3 != '':
                signer_list_with_order.append({'Signer':email3, 'RoutingOrder':routing3})

            if len(signer_list_with_order) == 0:
                #print('This subcontract has not been submitted yet. Please make sure that you have clicked the submit subcontract button before sending the subcontract out for endorsement.')
                messagebox.showinfo("Vista by Viewpoint",  "This subcontract has no recipients. Please add recipients based on template type.")
                master.destroy()
                return
            
            ## For clarity: always download the template whether it exists or not in the Template path. This fixed an error in Bravo. A previously stayed template can cause a problem.
            download_template(CLIENT_ID, os.path.join(base_dir, TEMPLATES_PATH, template_name))

            #Get template id from template name
            template_id, tmplt_name = check_template_name(template_name)
            actual_template_name = tmplt_name

            pb1['value'] = 20
            master.update_idletasks()

            template_source_path = ''
            if edited_template_name != '':   #If user has edited the template, then take the edited one, otherwise take the template from Templates folder
                TEMPLATE_FILENAME = str(edited_template_name).strip()
                template_source_path = os.path.join(base_dir, EDITED_TEMPLATES_PATH)
            else:
                TEMPLATE_FILENAME = str(template_name).strip()
                template_source_path = os.path.join(base_dir, TEMPLATES_PATH)

            ##Check if the edited template is available physically in the location
            edited_file_location = os.path.join(base_dir, template_source_path, TEMPLATE_FILENAME)
            
            if not os.path.exists(edited_file_location):
                TEMPLATE_FILENAME = str(template_name).strip()
                template_source_path = os.path.join(base_dir, TEMPLATES_PATH)
                download_template(CLIENT_ID, os.path.join(base_dir, template_source_path, TEMPLATE_FILENAME))

            pb1['value'] = 60
            master.update_idletasks()
            envelopes_api, folders_api = initialize_docusign_api(company)
            final_docx, final_merged_pdf, ticket_number, service_date = from_template_azure_blob(os.path.join(template_source_path, TEMPLATE_FILENAME), TEMPLATE_FILENAME, company, workorder, scope, scope_begin, scope_end, show_work_date_range, attachment_list, status_text, master, folders_api)

            if final_docx is None:
                messagebox.showerror("Vista by Viewpoint: No data found on Viewpoint database for this template", 'Error: no data found on Viewpoint database for this template:' + str(TEMPLATE_FILENAME) + ', Company:' + str(company) + ', Work Order:' + str(workorder) + ', Scope:' + str(scope) + '. Please check Viewpoint DB urgently.')
                print('Error: no data found on Viewpoint database for this template ', TEMPLATE_FILENAME, 'Company', company, 'Work Order', workorder, 'Scope', scope,  '. Begin', scope_begin, '. End', scope_end, '. Please check Viewpoint DB urgently.')
                master.destroy()
                return

            #If there are 3 signers, we will keep the 3 rd signer signature table / fields. Otherwise, if there are 2 signers, we will delete the table for the 3dr signer
            manage_third_signer_field(len(signer_list_with_order), final_docx)

            #If Internal stamp use checkbox is selected, then keep the stamp table, otherwise dont keep the stamp table 
            #manage_stamp_field_textfields(final_docx, str(smworkscope['udUseSubcontractorStamp']).strip())

            pb1['value'] = 75
            master.update_idletasks()
            update_status_panel(status_text, master, "***Converting the document into pdf.")
            convert(final_docx, os.path.join(base_dir, DOCUSIGN_Ready_Path, final_merged_pdf))
            update_status_panel(status_text, master, "***Final pdf has been generated successfully.")

            subject_line = 'Signature Request from Bravo Target Safety for Work Order: ' + str(workorder) + " Work Date: " + service_date

            pb1['value'] = 90
            master.update_idletasks()
            #Docusign process starts here. Getting auth token
            token = create_jwt_grant_token(company)
            post_data = {'grant_type': 'urn:ietf:params:oauth:grant-type:jwt-bearer', 'assertion': token} 
            base_url = 'https://' + ACCOUNT_URL + '/oauth/token'
            r = requests.post(base_url, data=post_data)
            token = r.json()

            args = {
                "account_id": GET_DOCUSIGN_USER_ID(company),
                "base_path": DOCUSIGN_URL + '/restapi',
                "access_token": token['access_token'],
                "status": "sent",
                "filepath": os.path.join(base_dir, DOCUSIGN_Ready_Path, final_merged_pdf),  #if document visibility if off, final_merged_pdf will be the all combined attachments appended at the bottom of the main document
                "data": signer_list_with_order,
                "company_id": company,
                "cc_name": cc_name,
                "cc_email": cc_email,
                "subject_line": subject_line,
                #"UseSubcontractorStamp": str(smworkscope['udUseSubcontractorStamp']).strip()
                "UseSubcontractorStamp": "Y"    #By default, by force making it Y
            }

            envelope = worker(args)
            update_status_panel(status_text, master, "***DocuSign workflow has been initiated.")
            # first_reminder_day = DEFAULT_REMINDER_FIRST_DAY
            # reminder_frequency = DEFAULT_REMINDER_FREQUENCY
            # days_before_expirity = DEFAULT_ENVELOPE_EXPIRY
            # days_before_expire_warning = DEFAULT_WARNING_BEFORE_EXPIRY
            # send_reminder_expiry(envelope['envelope_id'], first_reminder_day, reminder_frequency, days_before_expirity, days_before_expire_warning, token['access_token'])
            if scope == 0:   #If no scope is provided for the field ticket, then insert the signed contract into Scope = 1
                scope = 1
            insert_attachment_vpattachment_database_azure(base_dir, final_merged_pdf, envelope['envelope_id'], company, workorder, scope, smworkorderscopeid, ticket_number, service_date)

            #Prepare signer info on [SigningRequest] table
            for item in signer_list_with_order:
                insert_signing_request(str(item['Signer']).split('@')[0], item['Signer'], cc_name, cc_email, int(item['RoutingOrder']), envelope['envelope_id'], 'N', datetime.now().strftime('%Y-%m-%d %H:%M:%S'), None, 
                int(company),  str(workorder), str(scope), int(template_id), 'docusign' )

            ############### Cleaning phase started .. Clear the edited template textbox, clear the attachments area, Delete the Edited template if any, delete the template docx file.
            try:
                template_filepath = os.path.join(base_dir, TEMPLATES_PATH, str(template_name).strip())
                if os.path.exists(template_filepath):
                    os.remove(template_filepath)
            except:
                pass

            update_edit_template_textbox_value(company, workorder, scope, '')
            update_udfields_textbox_value(company, workorder, scope, 'udEditedTemplate')
            update_udfields_textbox_value(company, workorder, scope, 'udEmailCC')
            update_udfields_textbox_value(company, workorder, scope, 'udWorkOrderScopeBegin')
            update_udfields_textbox_value(company, workorder, scope, 'udWorkOrderScopeEnd')
            update_udfields_textbox_value(company, workorder, scope, 'udWorkOrderScope')
            update_udfields_textbox_value(company, workorder, scope, 'udTicketNumber')

            cleanup_folders(0)
            ############### Cleaning phase completed

            # Loading the custom vista table, using the ticket number generator.. MUST WORK ON IT LATER. WORK ON POLLING SCRIPT as well
            load_ticket_number_lookup_table(ticket_number, envelope['envelope_id'])

            pb1['value'] = 100
            master.update_idletasks()
            messagebox.showinfo("Vista by Viewpoint",  "Docusign workflow started. Once signatures are completed, the signed document will be visible here.")
            master.destroy()

        except Exception as e:
            exc_type, exc_obj, exc_tb = sys.exc_info()
            fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            print(exc_type, fname, exc_tb.tb_lineno)

            messagebox.showinfo("Vista by Viewpoint",  "DocuSign exception occured. Details: " + str(exc_type) + "\r\n" + str(fname) + "\r\n" + str(exc_tb.tb_lineno) + '\r\n' + str(e))
            master.destroy()
            return

    master = tkinter.Tk()
    # Set the width and height of our root window.
    master_window_height = 300
    master.geometry("900x300")
    master.title('Vista by Viewpoint: Docusign Workflow Started')

    pb1 = Progressbar(master, orient=HORIZONTAL, length=100, mode='determinate')
    pb1.pack(pady = 10)


    # Create label
    l = Label(master, text = "DocuSign In Progress")
    l.config(font =("Arial", 14))
    l.pack()

    status_label = Label(master,text = "Status").place(x = 50, y=master_window_height-180) 
    status_text = Text(master, height = 8, width = 65)
    status_text.config(font =("Arial", 9))
    status_text.place(x=110, y=master_window_height-180)
    update_status_panel(status_text, master, "***DocuSign workflow started.")

    master.after(2, start_docusign_within_tkinter)
    master.mainloop()

if __name__ == "__main__":
    main()