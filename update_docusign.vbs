Dim scriptdir, parentdir, filesys

Set filesys = CreateObject("Scripting.FileSystemObject")
scriptdir = filesys.GetParentFolderName(WScript.ScriptFullName)
parentdir = filesys.GetParentFolderName(filesys.GetParentFolderName(WScript.ScriptFullName))

Set WshShell = CreateObject("WScript.Shell" )
WshShell.Run parentdir & "\Kernel\App\Python\python.exe" & " " & scriptdir & "\update_docusign.py", 0, False

'Clear the objects at the end of your script.
set Arg = Nothing
Set WshShell = Nothing