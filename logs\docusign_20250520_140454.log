2025-05-20 14:04:54,050 - docusign_logger - INFO - DocuSign process started
2025-05-20 14:04:54,691 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 24140. 5, Username = olsen.sql.p4m
2025-05-20 14:04:56,304 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-05-20 14:04:58,892 - docusign_logger - INFO - Bulk send will send signature request to 76 employees.
2025-05-20 14:05:10,475 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2904}
2025-05-20 14:05:11,764 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwCAdVOg2ZfdSAgAgN0XAuKX3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.cBMk_2JuIylJK-AETohoq3wRrH7amXTijgPj0PxXgn4Y3F6JWEkoQFOkEPS2miP4pA1Udjda2SKSmPSWsXinpYi6zTE77DNL93vsZ_seKKMkcs6cGmX_av_vgeYNuhT_AjZWksO6385iSAUv5bEmzlykyTczKke7Mr5e3QAMYnpWlsBQLbvCFOKMubN6zE45NWXMScNuQmzxzVvpXq1mnQdmJ2eworM5Q-ghtZh2Hb392tQ_xtAdMaoRDZqgYxhFncq8BE6jRhVpzF-m_yj1mVuqs8WEBQFgMOtFu0D4Kdy8jFjuPV6y7n7-lHucAS4d2cZ2VtSuN_2obC59GKQlXw
2025-05-20 14:05:11,765 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-05-20 14:05:21,935 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-05-20 14:05:21,938 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-05-20 14:05:21,956 - docusign_logger - ERROR - ==================================================
2025-05-20 14:05:21,956 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-05-20 14:05:21,956 - docusign_logger - ERROR - Type: FileNotFoundError
2025-05-20 14:05:21,957 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-05-20 14:05:21,957 - docusign_logger - ERROR - Traceback:
2025-05-20 14:05:21,957 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-05-20 14:05:21,957 - docusign_logger - ERROR - ==================================================
2025-05-20 14:05:21,958 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
