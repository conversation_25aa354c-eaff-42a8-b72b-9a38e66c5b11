2025-04-28 14:25:11,610 - docusign_logger - INFO - DocuSign process started
2025-04-28 14:25:12,004 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-04-28 14:25:13,544 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-28 14:25:16,668 - docusign_logger - INFO - Bulk send will send signature request to 75 employees.
2025-04-28 14:25:24,566 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2527}
2025-04-28 14:25:25,413 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwCAqNbOkobdSAgAgBCbMJuG3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.jHhZyzKYVebINSi7IajNz_e84Lzb2K6HhXipNbGvUUC_tp-Z7pVROkkYZymxk_zDDURW-hdUxqCi7M1oT0f9MSq_XPVn4iMaEdU7K-yQNPBM2mg9RUJKzIW6RjOVsuRGVWxeZGpH3eExG7Pj4Q4ONz_11HSr9ubqcyQ8B_tu80nmTZ3m6YuElu47JlPaoeIp8FwUs_kIC6EiYHcVSxqMOHspDW4KQ24kQIl4pbuIb_LZhKAZs11EeCyni_1hiMOOl0mStFoscAjGIwMHMu9b0o6m38fzi4F8qncBQJZRYjfjD8BNvMGdq0GhHJilNvo6lAl94WGO8uim-OOa_emlNg
2025-04-28 14:25:25,413 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-28 14:25:33,736 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-04-28 14:25:33,736 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-28 14:25:33,753 - docusign_logger - ERROR - ==================================================
2025-04-28 14:25:33,753 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-28 14:25:33,753 - docusign_logger - ERROR - Type: FileNotFoundError
2025-04-28 14:25:33,753 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-04-28 14:25:33,753 - docusign_logger - ERROR - Traceback:
2025-04-28 14:25:33,753 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-04-28 14:25:33,753 - docusign_logger - ERROR - ==================================================
2025-04-28 14:25:33,753 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
