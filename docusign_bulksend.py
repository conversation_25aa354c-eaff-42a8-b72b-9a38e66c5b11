import json
import base64
import os
import time
from io import BytesIO
import requests
from docusign_esign.models import (
    Document, Signer, EnvelopeDefinition, BulkSendingCopyRecipient,
    BulkSendingCopyCustomField, Tabs, SignHere, Text, InitialHere, DateSigned,
    BulkSendingCopyTab, CustomFields, TextCustomField, BulkSendingList,
    BulkSendingCopy, Recipients, BulkSendRequest
)
from docusign_esign.apis import BulkEnvelopesApi, EnvelopesApi
from docusign_esign import (
    ApiClient, BulkSendingList, BulkSendingCopy, EnvelopeDefinition, Document,
    Signer, SignHere, Text, DateSigned, Tabs
)

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from PyPDF2 import PdfReader, PdfWriter
from config import Config
import re
import threading
import tkinter as tk
from tkinter import messagebox
from exception_logger import *
logger = setup_logger()


class DocuSignBulkSender:
    def __init__(self, envelopes_api):
        self.envelopes_api = envelopes_api
        self.access_token = self.envelopes_api.api_client.default_headers["Authorization"].split("Bearer ")[1]
        self.api_base_path = Config.DS_BASE_PATH
        self.account_id = Config.DS_ACCOUNT_ID
        self.api_client = self.envelopes_api.api_client
        self.api_client.host = Config.DS_BASE_PATH
        
        self.api_client.set_default_header(header_name="Authorization", header_value=f"Bearer {self.access_token}")
        
        self.base_path = Config.DS_BASE_PATH


        # api_client = ApiClient()
        # envelopes_api = initialize_docusign_api()
        # access_token = envelopes_api.api_client.default_headers["Authorization"].split("Bearer ")[1]
        # api_base_path = "https://demo.docusign.net/restapi"
        # account_id = "e1e226d7-9024-42f1-a2af-253c39d7eb7d"

        # # Construct your API headers
        # api_client.host = api_base_path
        # api_client.set_default_header(header_name="Authorization", header_value=f"Bearer {access_token}")

    def prepare_bulk_recipients(self, recipients_data):
        if not recipients_data:
            logger.error("Recipient data is empty!")
            raise ValueError("Recipient data is empty!")

        bulk_copies = []

        for recipient in recipients_data:
            # Create a recipient with custom fields for merge field replacement
            bulk_recipient = BulkSendingCopyRecipient(
                role_name=recipient["role_name"],
                name=recipient["name"],
                email=recipient["email"]
            )

            # Define custom fields using BulkSendingCopyCustomField (not TextCustomField)
            
            custom_fields = [
                BulkSendingCopyCustomField(name="Recipient Name", value=recipient["name"]),
                BulkSendingCopyCustomField(name="Recipient Email", value=recipient["email"]),
                BulkSendingCopyCustomField(name="Recipient Phone", value=recipient.get("phone", ""))
            ]

            # Add additional custom fields if they exist
            for field in ["PRCo", "Job", "Employee"]:
                if field in recipient:
                    custom_fields.append(BulkSendingCopyCustomField(name=field, value=str(recipient[field])))

            # Create a bulk copy with the defined recipients and custom fields
            bulk_copy = BulkSendingCopy(
                recipients=[bulk_recipient],
                custom_fields=custom_fields  # Include the custom fields here
            )

            bulk_copies.append(bulk_copy)

        return bulk_copies

    def create_envelope_definition(self, recipient):
        if not os.path.exists(recipient["document_path"]):
            logger.error(f"Document file not found: {recipient['document_path']}")
            raise FileNotFoundError(f"Document file not found: {recipient['document_path']}")
        
        with open(recipient["document_path"], "rb") as file:
            doc_base64 = base64.b64encode(file.read()).decode("utf-8")

        document = Document(
            document_base64=doc_base64,
            name=recipient["Job"],
            file_extension="pdf",
            document_id=recipient["document_id"]
        )

        # Add placeholder tabs
        recipient_sign_here = SignHere(
            anchor_string="/sig1/",
            anchor_units="pixels",
            anchor_y_offset="0",
            anchor_x_offset="0",
            tab_label="RecipentTab"
        )
        
        recipient_text_name = Text(
            anchor_string="/n1/",
            anchor_units="pixels",
            anchor_x_offset="0",
            anchor_y_offset="-2",
            font="Arial",
            font_size="Size10",
            locked="true",
            tab_label="SignerName",
            width="150"
        )
        
        recipient_text_phone = Text(
            anchor_string="/c1/",
            anchor_units="pixels",
            anchor_y_offset="0",
            anchor_x_offset="-2",
            font="Arial",
            font_size="Size10",
            locked="true",
            tab_label="SignerPhone",
            width="150"
        )

        recipient_text_email = Text(
            anchor_string="/e1/",
            anchor_units="pixels",
            anchor_y_offset="0",
            anchor_x_offset="-2",
            font="Arial",
            font_size="Size10",
            locked="true",
            tab_label="SignerEmail",
            width="150"
        )
        
        recipient_initial = InitialHere(
            anchor_string="/i1/",
            anchor_units="pixels",
            anchor_y_offset="0",
            anchor_x_offset="0",
            tab_label="RecipientInitial"
        )

        date_signed=[
            DateSigned(
                anchor_string="/d1/",
                anchor_units="pixels",
                anchor_x_offset="0",
                anchor_y_offset="0",
                font="Arial",
                font_size="Size10",
                tab_label="DateSigned"
            )
        ]
        

        # Add placeholder recipients
        signer = Signer(
            name="Multi Bulk Recipient::signer",
            email="<EMAIL>",
            role_name="signer",
            routing_order="1",
            recipient_id="1",
        )

        signer.tabs = Tabs(
            sign_here_tabs=[recipient_sign_here], 
            text_tabs=[recipient_text_name, recipient_text_phone, recipient_text_email],
            initial_here_tabs=[recipient_initial],
            date_signed_tabs=date_signed
        )

        custom_fields = CustomFields(
            text_custom_fields=[
                TextCustomField(name="Recipient Name", required="false", value=recipient["name"]),          
                TextCustomField(name="PRCo", required="false",  value=recipient["PRCo"]),
                TextCustomField(name="Job", required="false",  value=recipient["Job"]),
                TextCustomField(name="Employee", required="false", value=recipient["Employee"])
            ]
        )

        envelope_definition = EnvelopeDefinition(
            email_subject=f"Job-[[ECF:Job]]: Signature Request for [[ECF:Recipient Name]]",
            email_blurb=f"Hello [[ECF:Recipient Name]],\nPlease review and sign for Job-[[ECF:Job]]",
            documents=[document],
            recipients={"signers": [signer]},
            custom_fields=custom_fields, 
            status="created"
        )

        envelope_definition.recipients = Recipients(signers=[signer])

        return envelope_definition
    

    def create_bulk_sending_list(self, signers):
        print(signers[0])
        bulk_copies = []
        for signer in signers:
            recipient_1 = BulkSendingCopyRecipient(
                role_name="signer",
                name=signer["name"],
                email=signer["email"],
                tabs=[
                    BulkSendingCopyTab(
                            tab_label = "SignerName",
                            initial_value = signer["name"]
                    ),
                    BulkSendingCopyTab(
                            tab_label = "SignerPhone",
                            initial_value = signer["phone"]
                    ),
                    BulkSendingCopyTab(
                            tab_label = "SignerEmail",
                            initial_value = signer["email"]
                        )
                ]
            )

            bulk_copy = BulkSendingCopy(
                recipients=[recipient_1],
                custom_fields=[
                    BulkSendingCopyCustomField(name="Recipient Name", value=signer["name"]),
                    BulkSendingCopyCustomField(name="PRCo", value=signer["PRCo"]),
                    BulkSendingCopyCustomField(name="Job", value=signer["Job"]),
                    BulkSendingCopyCustomField(name="Employee", value=signer["Employee"])
                ]
            )

            bulk_copies.append(bulk_copy)

        bulk_sending_list = BulkSendingList(
            name="Bulk Send List for Job-" + str(signers[0]["Job"]),
            bulk_copies=bulk_copies
        )

        return bulk_sending_list
    


    def send_bulk_documents(self, recipients_data):
        if not recipients_data:
            logger.error("No recipients provided for bulk send.")
            raise ValueError("No recipients provided for bulk send.")
        
        # Submit a bulk list
        bulk_envelopes_api = BulkEnvelopesApi(self.api_client)
        bulk_sending_list = self.create_bulk_sending_list(recipients_data)
        bulk_list = bulk_envelopes_api.create_bulk_send_list(account_id = Config.DS_ACCOUNT_ID, bulk_sending_list = bulk_sending_list)
        bulk_list_id = bulk_list.list_id

        # Create an envelope
        envelope_definition = self.create_envelope_definition(recipients_data[0])
        envelope = self.envelopes_api.create_envelope(account_id = Config.DS_ACCOUNT_ID, envelope_definition = envelope_definition)
        envelope_id = envelope.envelope_id

        # Attach your bulk list id to the envelope
        text_custom_fields = TextCustomField(name="mailingListId", required="false", show="false", value=bulk_list_id)
        custom_fields = CustomFields(list_custom_fields=[], text_custom_fields=[text_custom_fields])
        self.envelopes_api.create_custom_fields(account_id = Config.DS_ACCOUNT_ID, envelope_id = envelope_id, custom_fields = custom_fields)

        # Initiate bulk send
        bulk_send_request = BulkSendRequest(envelope_or_template_id=envelope_id)
        batch = bulk_envelopes_api.create_bulk_send_request(
            account_id = Config.DS_ACCOUNT_ID,
            bulk_send_list_id = bulk_list_id,
            bulk_send_request = bulk_send_request
        )
        batch_id = batch.batch_id

        # Confirm successful batch send
        response = bulk_envelopes_api.get_bulk_send_batch_status(account_id = Config.DS_ACCOUNT_ID, bulk_send_batch_id = batch_id)
        #print(f'Newly Created Batch status: {response} Batch ID: {batch_id}')
        logger.info(f'Newly Created Batch status: {response} Batch ID: {batch_id}')
        return batch_id





    def send_reminder_expiry(self, envelope_id, first_reminder_day, reminder_frequency, days_before_expirity, days_before_expire_warning, access_token):
        url = f"{self.base_path}/v2.1/accounts/{Config.DS_ACCOUNT_ID}/envelopes/{envelope_id}/notification"
        payload = {
            "useAccountDefaults": False,
            "reminders": {
                "reminderEnabled": True,
                "reminderDelay": int(first_reminder_day),
                "reminderFrequency": int(reminder_frequency)
            },
            "expirations": {
                "expireEnabled": True,
                "expireAfter": int(days_before_expirity),
                "expireWarn": int(days_before_expire_warning)
            }
        }
        headers = {
            'Accept': 'application/json',
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        response = requests.put(url, headers=headers, data=json.dumps(payload))
        print(f"Reminder & Expiry set for Envelope ID {envelope_id}. Response: {response.text}")

    def get_bulk_envelopes_from_batch(self, account_id, access_token, base_path, batch_id, max_retries=80, delay=10):   
        time.sleep(delay)  #Making a force delay to let the batch process complete.   
        api_client = ApiClient()
        api_client.host = base_path
        api_client.set_default_header("Authorization", f"Bearer {access_token}")
        bulk_api = BulkEnvelopesApi(api_client)
        envelopes_api = EnvelopesApi(api_client)
        
        attempt = 0
        while attempt < max_retries:
            print(f"Checking for envelopes in batch (Attempt {attempt + 1}/{max_retries})...")
            logger.info(f"Checking for envelopes in batch (Attempt {attempt + 1}/{max_retries})...")
            
            try:
                # Get batch status to check if processing is complete
                batch_status = bulk_api.get_bulk_send_batch_status(account_id=account_id, bulk_send_batch_id=batch_id)
                print(f"Batch status: {batch_status}")
                logger.info(f"Batch status: {batch_status}")
                
                # Check if the batch is in a completed state
                if hasattr(batch_status, 'action_status') and batch_status.action_status == "Complete":
                    envelope_ids = []
                    start_position = 0
                    
                    # Use pagination to get all envelopes
                    while True:
                        envelopes_result = bulk_api.get_bulk_send_batch_envelopes(
                            account_id=account_id,
                            bulk_send_batch_id=batch_id,
                            start_position=start_position,
                            count=100
                        )

                        #Collecting all envelope IDs. The most important part.
                        if hasattr(envelopes_result, 'envelopes') and envelopes_result.envelopes:
                            #Adding all the envelope ids from the current pagination to the list.
                            envelope_ids.extend([env.envelope_id for env in envelopes_result.envelopes if hasattr(env, 'envelope_id') and env.envelope_id])
                        
                        # Check if we need to get more pages - in the API it's next_uri that indicates pagination
                        if not hasattr(envelopes_result, 'next_uri') or not envelopes_result.next_uri or not envelopes_result.envelopes:
                            break
                        
                        # Update start position for next page - extract from next_uri or use end_position
                        if hasattr(envelopes_result, 'end_position'):
                            start_position = envelopes_result.end_position
                        else:
                            # Try to extract from next_uri if end_position is not available
                            start_pos_match = re.search(r'start_position=(\d+)', envelopes_result.next_uri)
                            if start_pos_match:
                                start_position = start_pos_match.group(1)
                            else:
                                # If we can't determine the next position, break to avoid infinite loop
                                break
                    
                    # Verify we have the right number of envelopes
                    batch_size = int(batch_status.batch_size)
                    print(f'Batch size: {batch_size}')
                    logger.info(f'Batch size: {batch_size}')

                    # Get the custom fields and generate the mapping
                    envelope_data = []
                    for envelope_id in envelope_ids:
                        try:
                            # Get custom fields for the envelope
                            custom_fields = envelopes_api.list_custom_fields(account_id, envelope_id)
                            #print(f'------------------Custom fields: {custom_fields} from envelope ID: {envelope_id}')
                            logger.info(f'------------------Custom fields: {custom_fields} from envelope ID: {envelope_id}')
                            # Initialize envelope info dictionary
                            envelope_info = {
                                "envelope_id": envelope_id,
                                "PRCo": None,
                                "Job": None,
                                "Employee": None
                            }
                            
                            # Extract custom field values
                            if hasattr(custom_fields, 'text_custom_fields') and custom_fields.text_custom_fields:
                                for field in custom_fields.text_custom_fields:
                                    if field.name == "PRCo":
                                        envelope_info["PRCo"] = field.value
                                    elif field.name == "Job":
                                        envelope_info["Job"] = field.value
                                    elif field.name == "Employee":
                                        envelope_info["Employee"] = field.value
                            
                            envelope_data.append(envelope_info)
                            
                        except Exception as e:
                            logger.error(f"Error retrieving custom fields for envelope {envelope_id}: {str(e)}")
                            print(f"Error retrieving custom fields for envelope {envelope_id}: {str(e)}")
                            # Still include the envelope ID even if we couldn't get custom fields
                            envelope_data.append({"envelope_id": envelope_id, "PRCo": None, "Job": None, "Employee": None})
                    
                    if len(envelope_data) != batch_size:
                        print(f'Number of envelopes = {len(envelope_ids)} AND Batch size = {batch_size} not matching')
                        logger.warning(f'Number of envelopes = {len(envelope_ids)} AND Batch size = {batch_size} not matching')

                    return envelope_data


                
                # Handle cases where action_status isn't "Complete"
                elif hasattr(batch_status, 'action_status'):
                    print(f"Batch not yet completed. Current action_status: {batch_status.action_status}\n\n")
                    logger.info(f"Batch not yet completed. Current action_status: {batch_status.action_status}\n\n")
                else:
                    print("Cannot determine batch status. Will retry.\n\n")
                    logger.info("Cannot determine batch status. Will retry.\n\n")
                    
            except Exception as e:
                print(f"Error in attempt {attempt + 1}: {str(e)}")
                logger.error(f"Error in attempt {attempt + 1}: {str(e)}")
            attempt += 1
            time.sleep(delay)

        raise Exception("Timeout: Envelopes not yet available for the bulk batch.")

    def set_reminder_expiry(self, access_token, batch_id):
        envelope_data = self.get_bulk_envelopes_from_batch(account_id=Config.DS_ACCOUNT_ID,access_token=access_token,base_path=self.base_path,batch_id=batch_id)
        #print(f'Envelope data: {envelope_data}')
        logger.info(f'Envelope data: {envelope_data}, type: {type(envelope_data)}')

        envelope_ids = []
        for item in list(envelope_data):
            envelope_ids.append(item["envelope_id"])

        envelope_ids = list(set(envelope_ids))

        #print(f'Envelope IDs: {envelope_ids}')
        logger.info(f'Envelope IDs: {envelope_ids}')
        for env_id in envelope_ids:
            self.send_reminder_expiry(
                envelope_id=env_id,
                first_reminder_day=Config.DEFAULT_REMINDER_FIRST_DAY,
                reminder_frequency=Config.DEFAULT_REMINDER_FREQUENCY,
                days_before_expirity=Config.DEFAULT_ENVELOPE_EXPIRY,
                days_before_expire_warning=Config.DEFAULT_WARNING_BEFORE_EXPIRY,
                access_token=access_token
            )
        return envelope_data
    
    def add_batch_info_into_custom_tracker(self, employee_details, envelope_data):
        """
        Adds envelope IDs to employee details using the mapping from envelope_data
        
        Args:
            employee_details: List of employee detail dictionaries
            envelope_data: List of dictionaries containing envelope IDs and custom fields
        
        Returns:
            list: Updated employee_details with envelope_id added to each record
        """
        # Create a lookup for envelope IDs by composite key
        envelope_lookup = {}
        for env_info in envelope_data:
            if all(k in env_info and env_info[k] for k in ["PRCo", "Job", "Employee", "envelope_id"]):
                key = (str(env_info["PRCo"]), str(env_info["Job"]), str(env_info["Employee"]))
                envelope_lookup[key] = env_info["envelope_id"]
        
        # Create a new list for updated employee details
        updated_employee_details = []
        
        for emp in employee_details:
            # Create a copy of the employee dictionary to avoid modifying the original
            emp_copy = emp.copy()
            
            prco = str(emp.get('PRCo', ''))
            job_id = str(emp.get('Job', ''))
            employee_number = str(emp.get('Employee', ''))
            
            # Create composite key
            key = (prco, job_id, employee_number)
            
            if key in envelope_lookup:
                envelope_id = envelope_lookup[key]
                emp_copy['envelope_id'] = envelope_id
            else:
                print(f"No envelope found for PRCo={prco}, Job={job_id}, Employee={employee_number}")
                logger.info(f"No envelope found for PRCo={prco}, Job={job_id}, Employee={employee_number}")
                emp_copy['envelope_id'] = None
            
            updated_employee_details.append(emp_copy)
        
        return updated_employee_details
        

    def monitor_batch_and_set_reminders_async(self, access_token, batch_id, callback=None):
        """
        Starts a background thread to monitor batch status and set reminders when complete
        
        Args:
            access_token: DocuSign access token
            batch_id: Batch ID to monitor
            callback: Optional function to call when complete
        """
        def background_task():
            try:
                # Wait for batch to complete and get envelopes
                envelope_ids = self.get_bulk_envelopes_from_batch(account_id=self.account_id,access_token=access_token,base_path=self.base_path,batch_id=batch_id)             

                envelope_ids = list(set(envelope_ids))
                print('Final Envelope IDs: ', envelope_ids)
                
                # Set reminders for each envelope
                for env_id in envelope_ids:
                    self.send_reminder_expiry(
                        envelope_id=env_id,
                        first_reminder_day=Config.DEFAULT_REMINDER_FIRST_DAY,
                        reminder_frequency=Config.DEFAULT_REMINDER_FREQUENCY,
                        days_before_expirity=Config.DEFAULT_ENVELOPE_EXPIRY,
                        days_before_expire_warning=Config.DEFAULT_WARNING_BEFORE_EXPIRY,
                        access_token=access_token
                    )
                
                # Show completion message if we're in the main thread
                if callback:
                    # Use tkinter's after method to safely call from background thread
                    root = tk.Tk()
                    root.withdraw()  # Hide the root window
                    root.after(0, lambda: callback(envelope_ids))
                    root.mainloop()
            
            except Exception as e:
                print(f"Error in background thread: {str(e)}")
                if callback:
                    # Use tkinter's after method to safely call from background thread
                    root = tk.Tk()
                    root.withdraw()  # Hide the root window
                    root.after(0, lambda: callback(None, error=str(e)))
                    root.mainloop()
        
        # Start the background thread
        thread = threading.Thread(target=background_task)
        thread.daemon = True  # Make thread terminate when main program exits
        thread.start()
        
        return thread



    def check_bulk_batch_signing_status(self, account_id, access_token, batch_id):
        """
        Check the signing status of all envelopes in a bulk send batch
        
        Args:
            account_id (str): DocuSign account ID
            access_token (str): Authentication token
            batch_id (str): Bulk send batch ID
        
        Returns:
            dict: Comprehensive status of the batch
        """
        # Prepare to track envelope statuses
        batch_status = {
            "total_envelopes": 0,
            "signed_envelopes": 0,
            "pending_envelopes": 0,
            "failed_envelopes": 0,
            "envelope_details": []
        }
        
        try:
            # Create a new API client for envelope status checks
            api_client = ApiClient()
            api_client.host = self.base_path
            api_client.set_default_header("Authorization", f"Bearer {access_token}")
            
            # Initialize Bulk and Envelopes APIs
            bulk_api = BulkEnvelopesApi(api_client)
            envelopes_api = EnvelopesApi(api_client)
            
            # Get the batch status first
            try:
                batch_result = bulk_api.get_bulk_send_batch_status(
                    account_id=account_id, 
                    bulk_send_batch_id=batch_id
                )
                print(f"Batch Status: {batch_result}")
            except Exception as batch_status_error:
                print(f"Error getting batch status: {batch_status_error}")
                return batch_status
            
            # Get the envelopes in the batch
            start_position = 0
            while True:
                try:
                    envelopes_result = bulk_api.get_bulk_send_batch_envelopes(
                        account_id=account_id,
                        bulk_send_batch_id=batch_id,
                        start_position=start_position,
                        count=100
                    )
                    
                    # If no envelopes, break the loop
                    if not hasattr(envelopes_result, 'envelopes') or not envelopes_result.envelopes:
                        break
                    
                    # Process each envelope
                    for envelope in envelopes_result.envelopes:
                        if not hasattr(envelope, 'envelope_id'):
                            continue
                        
                        try:
                            # Get detailed envelope status
                            detailed_envelope = envelopes_api.get_envelope(
                                account_id=account_id, 
                                envelope_id=envelope.envelope_id
                            )
                            
                            # Prepare envelope details
                            envelope_detail = {
                                "envelope_id": envelope.envelope_id,
                                "status": detailed_envelope.status
                            }
                            
                            batch_status["envelope_details"].append(envelope_detail)
                            batch_status["total_envelopes"] += 1
                            
                            # Update status counters
                            if detailed_envelope.status == 'completed':
                                batch_status["signed_envelopes"] += 1
                            elif detailed_envelope.status in ['sent', 'delivered', 'waiting_for_others']:
                                batch_status["pending_envelopes"] += 1
                            else:
                                batch_status["failed_envelopes"] += 1
                        
                        except Exception as envelope_error:
                            print(f"Error processing individual envelope: {envelope_error}")
                    
                    # Check if there are more envelopes to fetch
                    if not hasattr(envelopes_result, 'next_uri') or not envelopes_result.next_uri:
                        break
                    
                    # Update start position for pagination
                    start_position = envelopes_result.end_position if hasattr(envelopes_result, 'end_position') else start_position + 100
                
                except Exception as batch_envelopes_error:
                    print(f"Error getting batch envelopes: {batch_envelopes_error}")
                    break
            
            return batch_status
        
        except Exception as e:
            print(f"Unexpected error in check_bulk_batch_signing_status: {e}")
            return batch_status
        



class DocuSignBulkManager:
    def __init__(self, account_id, access_token, base_path):
        self.account_id = account_id
        self.api_client = ApiClient()
        self.api_client.host = base_path
        self.api_client.set_default_header("Authorization", f"Bearer {access_token}")
        self.bulk_api = BulkEnvelopesApi(self.api_client)
        self.envelopes_api = EnvelopesApi(self.api_client)
        self.base_path = base_path