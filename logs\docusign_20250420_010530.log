2025-04-20 01:05:30,113 - docusign_logger - INFO - DocuSign process started
2025-04-20 01:05:30,687 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 1, Job = 25069., Username = olsen.sql.p4m
2025-04-20 01:05:32,222 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-20 01:05:34,166 - docusign_logger - INFO - BulkSend will send signature request to employee ranging from 1 to 3000
2025-04-20 01:05:34,720 - docusign_logger - INFO - Bulk send will send signature request to 68 employees.
2025-04-20 01:05:43,651 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {1107}
2025-04-20 01:05:44,804 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiNjgxODVmZjEtNGU1MS00Y2U5LWFmMWMtNjg5ODEyMjAzMzE3In0.AQoAAAABAAUABwAAFKHE2X_dSAgAAHxlJuJ_3UgCAP8ljtpWL-tMrlOF5voKDVgVAAEAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.zE6CmXO4-bV1lLoyvTCZ96R8UO7-z3-RBUVRQsmDiZ1fwNPD08zOWhrb_njSgksSlJD-CsOXBUkEFcLoOKCSWCxm5NmGj5ebTPG6bQGjyLThMwgbjA23esQWukE5wKLfZcTN3TkkeuAtAyQiKYvIvP9P4j8gYKLlNSUB8KNtYDitnf5pEbVSyZOjxXGuwqlt2qx42Lazx8Qt7cSox9TIQJx1YOhNQWNEEgwYwhM5v2OAzvpc3rNkE0X-2QlYRAWx5__PDF3aoFAm6sBUtTUSw6mj0OjM5dzILIu4F1Gk14i_1heADyd950TEqU0HaM_XkX6-gJRhj32G7chGH38jPw
2025-04-20 01:05:44,806 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-20 01:05:56,937 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.pdf', 'doc_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.doc', 'success': True, 'error': None}
2025-04-20 01:05:56,938 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-20 01:06:03,314 - docusign_logger - INFO - Newly Created Batch status: {'action': 'Send',
 'action_status': 'Processing',
 'batch_id': '53c1aa02-919a-4a08-bd5e-dc7e63d0da83',
 'batch_name': 'Bulk Send List for Job-25069',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': '70529d70-edbd-4480-9c57-2283a0c68422',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': None,
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/53c1aa02-919a-4a08-bd5e-dc7e63d0da83/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '8915fe9c-78c5-4f51-ad5d-8fb9e315274e',
 'mailing_list_name': 'Bulk Send List for Job-25069',
 'owner_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'queued': '1',
 'resends_remaining': 'N/A',
 'sender_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'sent': '0',
 'submitted_date': '2025-04-20T14:06:02.4070000Z'} Batch ID: 53c1aa02-919a-4a08-bd5e-dc7e63d0da83
2025-04-20 01:06:03,316 - docusign_logger - INFO - Bulk send initiated with batch ID: 53c1aa02-919a-4a08-bd5e-dc7e63d0da83
2025-04-20 01:06:03,318 - docusign_logger - INFO - Bulk send initiated successfully with Batch ID: 53c1aa02-919a-4a08-bd5e-dc7e63d0da83. Cleaning up the temporary files.
2025-04-20 01:06:03,325 - docusign_logger - INFO - Background process started to monitor batch and set reminders. Please wait.
2025-04-20 01:06:13,331 - docusign_logger - INFO - Checking for envelopes in batch (Attempt 1/80)...
2025-04-20 01:06:14,189 - docusign_logger - INFO - Batch status: {'action': 'Send',
 'action_status': 'Processing',
 'batch_id': '53c1aa02-919a-4a08-bd5e-dc7e63d0da83',
 'batch_name': 'Bulk Send List for Job-25069',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': '70529d70-edbd-4480-9c57-2283a0c68422',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': None,
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/53c1aa02-919a-4a08-bd5e-dc7e63d0da83/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '8915fe9c-78c5-4f51-ad5d-8fb9e315274e',
 'mailing_list_name': 'Bulk Send List for Job-25069',
 'owner_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'queued': '1',
 'resends_remaining': 'N/A',
 'sender_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'sent': '0',
 'submitted_date': '2025-04-20T14:06:02.4070000Z'}
2025-04-20 01:06:14,190 - docusign_logger - INFO - Batch not yet completed. Current action_status: Processing


2025-04-20 01:06:24,199 - docusign_logger - INFO - Checking for envelopes in batch (Attempt 2/80)...
2025-04-20 01:06:25,111 - docusign_logger - INFO - Batch status: {'action': 'Send',
 'action_status': 'Complete',
 'batch_id': '53c1aa02-919a-4a08-bd5e-dc7e63d0da83',
 'batch_name': 'Bulk Send List for Job-25069',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': '70529d70-edbd-4480-9c57-2283a0c68422',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': '1',
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/53c1aa02-919a-4a08-bd5e-dc7e63d0da83/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '8915fe9c-78c5-4f51-ad5d-8fb9e315274e',
 'mailing_list_name': 'Bulk Send List for Job-25069',
 'owner_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'queued': '0',
 'resends_remaining': '3',
 'sender_user_id': 'da8e25ff-2f56-4ceb-ae53-85e6fa0a0d58',
 'sent': '1',
 'submitted_date': '2025-04-20T14:06:02.4070000Z'}
2025-04-20 01:06:25,595 - docusign_logger - INFO - Batch size: 1
2025-04-20 01:06:25,923 - docusign_logger - INFO - ------------------Custom fields: {'list_custom_fields': [],
 'text_custom_fields': [{'configuration_type': None,
                         'error_details': None,
                         'field_id': '11191907059',
                         'name': 'Recipient Name',
                         'required': 'false',
                         'show': 'false',
                         'value': 'Aaron Stapon'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11191907060',
                         'name': 'PRCo',
                         'required': 'false',
                         'show': 'false',
                         'value': '1'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11191907061',
                         'name': 'Job',
                         'required': 'false',
                         'show': 'false',
                         'value': '25069'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11191907062',
                         'name': 'Employee',
                         'required': 'false',
                         'show': 'false',
                         'value': '1107'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '11191907063',
                         'name': 'BulkBatchId',
                         'required': 'false',
                         'show': 'false',
                         'value': '53c1aa02-919a-4a08-bd5e-dc7e63d0da83'}]} from envelope ID: f2940d1c-dd7f-4c74-87f5-4b3b292b0129
2025-04-20 01:06:25,925 - docusign_logger - INFO - Envelope data: [{'envelope_id': 'f2940d1c-dd7f-4c74-87f5-4b3b292b0129', 'PRCo': '1', 'Job': '25069', 'Employee': '1107'}], type: <class 'list'>
2025-04-20 01:06:25,925 - docusign_logger - INFO - Envelope IDs: ['f2940d1c-dd7f-4c74-87f5-4b3b292b0129']
2025-04-20 01:06:27,461 - docusign_logger - INFO - Logs have been inserted into custom tracker successfully.
2025-04-20 01:06:27,469 - docusign_logger - INFO - Logging signer info on tracking table.
2025-04-20 01:06:30,062 - docusign_logger - INFO - UD fields cleaned up successfully.
2025-04-20 01:06:30,072 - docusign_logger - INFO - Docusign workflow completed successfully.
