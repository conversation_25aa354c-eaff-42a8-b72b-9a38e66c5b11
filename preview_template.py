import subprocess
import json
import csv
import sys
from dbmodels import get_PREH_job_details, get_report_lookups, get_report_parameters
from utils import *
from tkinter import messagebox
import os
from datetime import datetime
from config import *
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import Querybox
from ttkbootstrap.scrolled import Scrolled<PERSON>rame
from tkinter.filedialog import asksaveasfile
import fitz
from tkinter import PhotoImage
import shutil
from crystal_report_converter import CrystalReportConverter
from config import Config
status_text = None  #for status update text
new_merged_filename = None
scrolled_frame = None

def initialize_config(secret_data):
    # Database URLs
    Config.CONN_STR_VIEWPOINT = secret_data.get('VIEWPOINT_DB_URL')
    Config.CONN_STR_VPATTACHMENT = secret_data.get('VP_ATTACHMENTS_DB_URL')
    Config.CONN_STR_SMART_SIGN = secret_data.get('ESIGNATURE_DB_URL')
    
    # DocuSign configuration
    Config.DS_INTEGRATION_KEY = secret_data.get('DS_INTEGRATION_KEY')
    Config.DS_ACCOUNT_ID = secret_data.get('DS_ACCOUNT_ID')
    Config.DS_USER_ID = secret_data.get('DS_USER_ID')
    Config.DS_PRIVATE_KEY_FILE = secret_data.get('DS_PRIVATE_KEY_FILE').encode('utf-8')
    Config.DS_AUTHORIZATION_SERVER = secret_data.get('DS_AUTHORIZATION_SERVER')
    Config.DS_BASE_URL = secret_data.get('DS_BASE_URL')
    Config.DS_ACCOUNT_URL = secret_data.get('DS_ACCOUNT_URL')
    Config.DS_BRAND_ID = secret_data.get('DS_BRAND_ID')
    Config.DS_BASE_PATH = secret_data.get('DS_BASE_PATH') + '/restapi'

def start_preview_process(report_filename, report_parameters, master_param_dict):
    # Get parameters from the CSV
    parameters_dict = json.loads(report_parameters)

    if parameters_dict is None:
        print(f"No parameters found for report file: {report_filename}")
        messagebox.showerror("Error", "No parameters found for report file: {report_filename}")
        return

    print(f"Parameters for {report_filename}: {parameters_dict}")

    for param_key, param_type in parameters_dict.items():
        matching_key = next((k for k in master_param_dict if k.lower() in param_key.lower()), None)
        
        if matching_key:
            if param_type == 'NumberParameter':
                try:
                    parameters_dict[param_key] = int(master_param_dict[matching_key])
                except ValueError:
                    print(f"Warning: Could not convert {matching_key} to a number. Using original value.")
            elif param_type == 'DateParameter':
                try:
                    parameters_dict[param_key] = str(master_param_dict[matching_key])
                except ValueError:
                    print(f"Warning: Could not convert {matching_key} to a date. Using original value.")
            else:  # Treat as StringParameter or any other type
                parameters_dict[param_key] = str(master_param_dict[matching_key])


    print(f'\n\nFinal parameters: {parameters_dict}')
    parameters_json = json.dumps(parameters_dict)


    # Call the C# application with the template filename and parameters as arguments. This is the crystal report converter
    exe_file_path = os.path.join(base_dir, "CrystalReportManager", "CrystalReportDemo.exe")
    try:
        print(f"Executing command: {[exe_file_path, report_filename.replace('.rpt', ''), parameters_json]}")
        result = subprocess.run(
            [exe_file_path, report_filename.replace('.rpt', ''), parameters_json],
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE, 
            text=True,
            check=True
        )
        
        # Capture the log output
        logs = result.stdout.strip()
        print(f"C# script log: \n\n{logs}")
        log_lines = logs.splitlines()
        pdf_lines = [line for line in log_lines if line.startswith("***###")]
        if pdf_lines:
            last_pdf_line = pdf_lines[-1]
            pdf_filename = last_pdf_line.replace("***###", "").strip()
            pdf_filepath = os.path.join(base_dir, "CrystalReportManager", "Export", pdf_filename)
            print(f"Full PDF file path: {pdf_filepath}")
        else:
            print("No PDF file information found in the logs.")

    except subprocess.CalledProcessError as e:
        print(f"An error occurred: {e.stderr}")

def update_pdf_view():
    global pdf_document, scrolled_frame
    
    if pdf_document is None:
        messagebox.showerror("Error", "No PDF document loaded.")
        return

    # Clear existing content in the ScrolledFrame
    for widget in scrolled_frame.winfo_children():
        widget.destroy()

    # Display each page of the PDF
    for page_num in range(len(pdf_document)):
        page = pdf_document.load_page(page_num)
        pix = page.get_pixmap(matrix=fitz.Matrix(zoom_factor, zoom_factor))
        img = ttk.PhotoImage(data=pix.tobytes("ppm"))
        label = ttk.Label(scrolled_frame, image=img)
        label.image = img  # Keep a reference
        label.pack(pady=10)

def load_pdf(pdf_path):
    global pdf_document
    if os.path.exists(pdf_path):
        pdf_document = fitz.open(pdf_path)
        return True
    else:
        messagebox.showerror("Error", f"PDF file not found: {pdf_path}")
        return False

# Entry point of the script
def main():
    def save_file():
        file_types = [('Pdf Files', '*.pdf'), ('All Files', '*.*')]
        f = asksaveasfile(filetypes = file_types,  defaultextension=file_types)
        if f is None: # asksaveasfile return `None` if dialog closed with "cancel".
            return
        destination_path = f.name
        print(os.path.join(base_dir, Config.DOCUSIGN_Ready_Path, new_merged_filename), 'Saving Into:', destination_path)
        shutil.copy(os.path.join(base_dir, Config.DOCUSIGN_Ready_Path, new_merged_filename), destination_path)
        messagebox.showinfo("Vista by Viewpoint",  "File has been saved successfully.")

    def close_window():
        master.destroy()

    def zoom_in():
        global zoom_factor
        zoom_factor += 0.2
        update_pdf_view()

    def zoom_out():
        global zoom_factor
        zoom_factor = max(0.2, zoom_factor - 0.2)  # Prevent zooming out too much
        update_pdf_view()

    def preview_template():
        #try:
        ##Cleaning required folders
        base_dir = os.path.dirname(os.path.realpath(__file__))
        os.chdir(base_dir)
        templates_directory = os.path.join(base_dir, "Templates")
        docusign_ready_directory = os.path.join(base_dir, "Docusignready")
        docusign_completed_directory = os.path.join(base_dir, "Docusigncompleted")
        delete_contents_of_dir(templates_directory) 
        delete_contents_of_dir(docusign_ready_directory)
        delete_contents_of_dir(docusign_completed_directory)

        # Get secrets and initialize config variables
        secret_data = extract_secrets_from_azure_key_vault(Config.AZURE_KEYVAULT_SECRET_VALUE)
        initialize_config(secret_data)


        #################### Parsing command line arguments #################
        args = parse_args()
        company =  str(args['Company']).strip()
        job = str(args['Job']).strip()

        employee_details = get_PREH_job_details(company, job)

        if len(employee_details) == 0:
            messagebox.showinfo("Vista by Viewpoint",  "No employee records found for this job. Please check if this job belongs to a certified payroll.")
            master.destroy()
            return

        
        update_status_panel(status_text, master, "***Using crystal report converter to export the report into pdf for DocuSign.")
        
        # # Call the C# application with the template filename and parameters as arguments. This is the crystal report converter
        parent_dir = os.path.dirname(base_dir)
        exe_file_path = os.path.join(parent_dir, "CrystalReportManager", "CrystalReportDemo.exe")
        report_filename = Config.CRYSTAL_REPORT_NAME
        converter = CrystalReportConverter(exe_file_path)
        parameters = {
            "Company": company,
            "Job": job,
            "AwardDate": employee_details[0]['udJobAwardDate'] if employee_details[0]['udJobAwardDate'] is not None and employee_details[0]['udJobAwardDate'] != '' else 'USE_DEFAULT',
            "StartDate": employee_details[0]['udStartDate'] if employee_details[0]['udStartDate'] is not None and employee_details[0]['udStartDate'] != '' else 'USE_DEFAULT',
            "MeetingDate": employee_details[0]['udMeetingDate'] if employee_details[0]['udMeetingDate'] is not None and employee_details[0]['udMeetingDate'] != '' else 'USE_DEFAULT',
            "MeetingType": employee_details[0]['udGroupOrIndividuals'] if employee_details[0]['udGroupOrIndividuals'] is not None and employee_details[0]['udGroupOrIndividuals'] != '' else 'USE_DEFAULT',
            "RepName": employee_details[0]['udRepresentativeName'] if employee_details[0]['udRepresentativeName'] is not None and employee_details[0]['udRepresentativeName'] != '' else 'USE_DEFAULT',
            "RepEmail": employee_details[0]['udRepresentativeEmail'] if employee_details[0]['udRepresentativeEmail'] is not None and employee_details[0]['udRepresentativeEmail'] != '' else 'USE_DEFAULT',
            "BegEmp": employee_details[0]['udBeginningEmployee'] if employee_details[0]['udBeginningEmployee'] is not None and employee_details[0]['udBeginningEmployee'] != '' else 'USE_DEFAULT',
            "EndEmp": employee_details[0]['udEndingEmployee'] if employee_details[0]['udEndingEmployee'] is not None and employee_details[0]['udEndingEmployee'] != '' else 'USE_DEFAULT',
            "EmpAltEmail": employee_details[0]['udEmployeeAlternativeEmail'] if employee_details[0]['udEmployeeAlternativeEmail'] is not None and employee_details[0]['udEmployeeAlternativeEmail'] != '' else 'USE_DEFAULT',
            "DateReportingFormat": 1
        }
        result = converter.generate_report(report_filename, parameters)
        print(result)
        if result['success']:
            pdf_filepath = result['pdf_path']
            doc_filepath = result['doc_path']
            # Copy files to DOCUSIGN_Ready_Path
            docusign_ready_dir = os.path.join(base_dir, Config.DOCUSIGN_Ready_Path)
            pdf_destination = os.path.join(docusign_ready_dir, os.path.basename(pdf_filepath))
            doc_destination = os.path.join(docusign_ready_dir, os.path.basename(doc_filepath))

            shutil.copy(pdf_filepath, pdf_destination)
            shutil.copy(doc_filepath, doc_destination)
            
            update_status_panel(status_text, master, f"***Files copied to DocuSign Ready folder: {os.path.basename(pdf_filepath)}")
            
            if load_pdf(pdf_destination):
                update_pdf_view()
            else:
                update_status_panel(status_text, master, "***Failed to load PDF.")
            
            # exe_file_path = os.path.join(base_dir, "CrystalReportManager", "CrystalReportDemo.exe")
            # try:
            #     print(f"Executing command: {[exe_file_path, report_filename.replace('.rpt', ''), parameters_json]}")
            #     result = subprocess.run(
            #         [exe_file_path, report_filename.replace('.rpt', ''), parameters_json],
            #         stdout=subprocess.PIPE, 
            #         stderr=subprocess.PIPE, 
            #         text=True,
            #         check=True
            #     )
                
            #     # Capture the log output generated by the C# code
            #     logs = result.stdout.strip()
            #     print(f"C# script log: \n\n{logs}")
            #     log_lines = logs.splitlines()

            #     marker_index = log_lines.index("***###")
            #     if marker_index < len(log_lines) - 1:  # Ensure there's data after the marker
            #         json_str = '\n'.join(log_lines[marker_index + 1:])
            #         file_data = json.loads(json_str)
            #         doc_filename = file_data.get('DocFilePath')
            #         pdf_filename = file_data.get('PdfFilePath')
                    
            #         print(f"DOC file: {doc_filename}")
            #         print(f"PDF file: {pdf_filename}")
                    
            #         if pdf_filename:
            #             # pdf_filepath = os.path.join(base_dir, "CrystalReportManager", "Export", pdf_filename)
            #             # doc_filepath = os.path.join(base_dir, "CrystalReportManager", "Export", doc_filename)
            #             pdf_filepath = pdf_filename
            #             doc_filepath = doc_filename
            #             print(f"Full PDF file path: {pdf_filepath} and the doc file path is {doc_filepath} ")
            #             update_status_panel(status_text, master, "***PDF file generated successfully.")
            #         else:
            #             print("No PDF file path found in the JSON data.")
            #             pdf_filepath = None
            #             doc_filepath = None
            #     else:
            #         print("No data found after the marker.")
            #         pdf_filepath = None
            #         doc_filepath = None

            # except Exception as e:
            #     print(f"An error occurred while generating the preview: {e}")

            ############ Hardcoding PDF filepath temporarily
            # pdf_filepath = 'C:\\ViewpointIntegrationEarle\\CrystalReportManager\\Export\\Tax Exempt.pdf'

            # global new_merged_filename, pdf_document
            # new_merged_filename = pdf_filepath
            # if pdf_filepath is None:
            #     messagebox.showerror("Error", "Failed to generate the preview.")
            #     sys.exit(1)

            # # Load PDF using PyMuPDF
            # pdf_path = os.path.join(base_dir, DOCUSIGN_Ready_Path, pdf_filepath)
            # if load_pdf(pdf_path):
            #     update_pdf_view()
            # else:
            #     update_status_panel(status_text, master, "***Failed to load PDF.")






        # except Exception as e:
        #     messagebox.showerror("Vista by Viewpoint", f"Error occurred while previewing the template. Details: {str(e)}")
        # finally:
        #     update_status_panel(status_text, master, "***Preview process completed.")

    # Initialize ttkbootstrap
    global scrolled_frame
    master = ttk.Window(themename="litera")
    master.title('Vista by Viewpoint: Preview Template')
    master.geometry("900x800")

    base_dir = os.path.dirname(os.path.realpath(__file__))
    os.chdir(base_dir)
    master.iconbitmap(os.path.join(base_dir, "icons", 'logo.ico'))

    # Create a frame for buttons
    button_frame = ttk.Frame(master)
    button_frame.pack(side=BOTTOM, fill=X, padx=10, pady=10)

    close_btn = ttk.Button(button_frame, text='Close', command=close_window, width=10)
    close_btn.pack(side=RIGHT, padx=5)

    save_btn = ttk.Button(button_frame, text='Save', command=save_file, width=10)
    save_btn.pack(side=RIGHT, padx=5)

    # Create and resize zoom icons
    icon_size = 20  # Adjust this value to change the icon size
    zoom_in_icon = PhotoImage(file=os.path.join(base_dir, "icons", "zoom_in.png"))
    zoom_in_icon = zoom_in_icon.subsample(int(zoom_in_icon.width() / icon_size))
    
    zoom_out_icon = PhotoImage(file=os.path.join(base_dir, "icons", "zoom_out.png"))
    zoom_out_icon = zoom_out_icon.subsample(int(zoom_out_icon.width() / icon_size))

    # Create zoom buttons with resized icons
    zoom_in_btn = ttk.Button(button_frame, image=zoom_in_icon, command=zoom_in, bootstyle="light-outline", width=icon_size+10)
    zoom_in_btn.image = zoom_in_icon  # Keep a reference to prevent garbage collection
    zoom_in_btn.pack(side=RIGHT, padx=5)

    zoom_out_btn = ttk.Button(button_frame, image=zoom_out_icon, command=zoom_out, bootstyle="light-outline", width=icon_size+10)
    zoom_out_btn.image = zoom_out_icon  # Keep a reference to prevent garbage collection
    zoom_out_btn.pack(side=RIGHT, padx=5)


    # Create a ScrolledFrame for PDF content
    scrolled_frame = ScrolledFrame(master)
    scrolled_frame.pack(fill=BOTH, expand=YES, padx=10, pady=10)

    # Status text
    status_text = ttk.Text(master, height=8, width=65, font=("Arial", 9))
    status_text.pack(side=BOTTOM, fill=X, padx=10, pady=10)
    update_status_panel(status_text, master, "***Preview started.")

    # Initialize global variables
    global zoom_factor, pdf_document
    zoom_factor = 1  # Initial zoom factor (slightly zoomed out)
    pdf_document = None

    master.after(100, preview_template)
    master.mainloop()

if __name__ == "__main__":
    main()