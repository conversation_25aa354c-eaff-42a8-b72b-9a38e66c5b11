2025-05-01 15:14:08,607 - docusign_logger - INFO - DocuSign process started
2025-05-01 15:14:08,792 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-05-01 15:14:10,210 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-05-01 15:14:12,826 - docusign_logger - INFO - Bulk send will send signature request to 74 employees.
2025-05-01 15:14:17,642 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2654}
2025-05-01 15:14:18,714 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwAAMUgi9YjdSAgAAJkMhP2I3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.k2DvWT-66ZBLG1ImLTS21GIwlhBkDriNJGD5lby1_4PXzai-FyJ_QFSj8a7_IOzJR82Jn25Q-gK1p_NQm221to5mZPl60GjN83AbDiOco5iF1QOzIt-0MD0G_Q5IULL5xQ_NbV1WyV1SXZoWIbU3-iL3PEL3jBCK47xMBLX_ytcmCwJGFncPosCVWGc7AVaZ_Obenm-6CssQVr2S68cu7xODCkNLQcWPfuG8zv3tkryaYDH6-hh6z73qcJ6WcKzcYGrdMBIHrC9uOVISlF99aky0wOhfRErCybi-c5300On5gfUr_2a9VjzhTNW-OJQ-ieehNyaMSzD-elZ2wEP1OA
2025-05-01 15:14:18,716 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-05-01 15:14:29,156 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.pdf', 'doc_path': 'C:\\ViewpointIntegrationEarle\\ViewpointDocusignBulk\\CrystalReportManager\\Export\\EAC MW212.doc', 'success': True, 'error': None}
2025-05-01 15:14:29,156 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-05-01 15:14:34,208 - docusign_logger - INFO - Newly Created Batch status: {'action': 'Send',
 'action_status': 'Processing',
 'batch_id': 'af099315-4300-4eef-be6f-85a7bcfa285d',
 'batch_name': 'Bulk Send List for Job-250565',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': 'f5fb4c05-b981-4295-9ed3-8a9d7a34164e',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': None,
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/af099315-4300-4eef-be6f-85a7bcfa285d/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '1e4edc07-01d1-4645-b4b9-ca386db2b6c2',
 'mailing_list_name': 'Bulk Send List for Job-250565',
 'owner_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'queued': '1',
 'resends_remaining': 'N/A',
 'sender_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'sent': '0',
 'submitted_date': '2025-05-02T04:14:33.3600000Z'} Batch ID: af099315-4300-4eef-be6f-85a7bcfa285d
2025-05-01 15:14:34,208 - docusign_logger - INFO - Bulk send initiated with batch ID: af099315-4300-4eef-be6f-85a7bcfa285d
2025-05-01 15:14:34,216 - docusign_logger - INFO - Bulk send initiated successfully with Batch ID: af099315-4300-4eef-be6f-85a7bcfa285d. Cleaning up the temporary files.
2025-05-01 15:14:34,218 - docusign_logger - INFO - Background process started to monitor batch and set reminders. Please wait.
2025-05-01 15:14:44,219 - docusign_logger - INFO - Checking for envelopes in batch (Attempt 1/80)...
2025-05-01 15:14:44,985 - docusign_logger - INFO - Batch status: {'action': 'Send',
 'action_status': 'Complete',
 'batch_id': 'af099315-4300-4eef-be6f-85a7bcfa285d',
 'batch_name': 'Bulk Send List for Job-250565',
 'batch_size': '1',
 'bulk_errors': [],
 'envelope_id_or_template_id': 'f5fb4c05-b981-4295-9ed3-8a9d7a34164e',
 'envelopes_info': {'authoritative_copy': None,
                    'completed': None,
                    'correct': None,
                    'created': None,
                    'declined': None,
                    'deleted': None,
                    'delivered': None,
                    'digital_signatures_pending': None,
                    'sent': '1',
                    'signed': None,
                    'timed_out': None,
                    'transfer_completed': None,
                    'voided': None},
 'envelopes_uri': '/bulk_send_batch/af099315-4300-4eef-be6f-85a7bcfa285d/envelopes?start_position=0&count=40',
 'failed': '0',
 'mailing_list_id': '1e4edc07-01d1-4645-b4b9-ca386db2b6c2',
 'mailing_list_name': 'Bulk Send List for Job-250565',
 'owner_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'queued': '0',
 'resends_remaining': '3',
 'sender_user_id': '96b0e1ee-de48-4933-b0af-a03f1d092e59',
 'sent': '1',
 'submitted_date': '2025-05-02T04:14:33.3600000Z'}
2025-05-01 15:14:45,270 - docusign_logger - INFO - Batch size: 1
2025-05-01 15:14:45,575 - docusign_logger - INFO - ------------------Custom fields: {'list_custom_fields': [],
 'text_custom_fields': [{'configuration_type': None,
                         'error_details': None,
                         'field_id': '5135769725',
                         'name': 'Recipient Name',
                         'required': 'false',
                         'show': 'false',
                         'value': 'Michael Witham'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5135769726',
                         'name': 'PRCo',
                         'required': 'false',
                         'show': 'false',
                         'value': '5'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5135769727',
                         'name': 'Job',
                         'required': 'false',
                         'show': 'false',
                         'value': '250565'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5135769728',
                         'name': 'Employee',
                         'required': 'false',
                         'show': 'false',
                         'value': '2654'},
                        {'configuration_type': None,
                         'error_details': None,
                         'field_id': '5135769729',
                         'name': 'BulkBatchId',
                         'required': 'false',
                         'show': 'false',
                         'value': 'af099315-4300-4eef-be6f-85a7bcfa285d'}]} from envelope ID: 8c38050d-2012-4d1f-a4e5-2d727f5a7324
2025-05-01 15:14:45,590 - docusign_logger - INFO - Envelope data: [{'envelope_id': '8c38050d-2012-4d1f-a4e5-2d727f5a7324', 'PRCo': '5', 'Job': '250565', 'Employee': '2654'}], type: <class 'list'>
2025-05-01 15:14:45,590 - docusign_logger - INFO - Envelope IDs: ['8c38050d-2012-4d1f-a4e5-2d727f5a7324']
2025-05-01 15:14:47,964 - docusign_logger - INFO - Logs have been inserted into custom tracker successfully.
2025-05-01 15:14:47,964 - docusign_logger - INFO - Logging signer info on tracking table.
2025-05-01 15:14:49,915 - docusign_logger - INFO - UD fields cleaned up successfully.
2025-05-01 15:14:49,919 - docusign_logger - INFO - Docusign workflow completed successfully.
