2025-04-28 16:29:44,372 - docusign_logger - INFO - DocuSign process started
2025-04-28 16:29:44,565 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 25056. 5, Username = olsen.sql.p4m
2025-04-28 16:29:46,123 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-04-28 16:29:49,546 - docusign_logger - INFO - Bulk send will send signature request to 75 employees.
2025-04-28 16:30:09,509 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2654}
2025-04-28 16:30:10,705 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwAABT88pIbdSAgAAG0DnqyG3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.hXkCvYBmXeXT6UIewEde_djtqogXLf0-PoSRrMHVF9lDCCHGhgeFRQqVeeOgtdC4hSuFFJfr-7z4HPX9NdIcg3EyHI-UGgF3DVryMmE0oIfHU0I63DoL2jhp0TZMEzZEg7Klw3kL96haf7GYYTPHfze-FBmzwcY3ciZ5g8ulmlzNZgqSNpHLzBZRlHFE4JqUDpbOE5FXGQYGXy1yMMsqPcUDlT3mvTymXFR694sATj_ckCIqPyTyg73tZ3KMNSXaJYZu-BI_EPkgSPFcwRpHNHBRo79mWCHyqwrvom75zpTrWYqCV4lFu1DtsVEeVUKiyWPTsVDI5d-YGjcurjcJTQ
2025-04-28 16:30:10,706 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-04-28 16:30:20,012 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-04-28 16:30:20,012 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-04-28 16:30:20,012 - docusign_logger - ERROR - ==================================================
2025-04-28 16:30:20,012 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-04-28 16:30:20,012 - docusign_logger - ERROR - Type: FileNotFoundError
2025-04-28 16:30:20,012 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-04-28 16:30:20,012 - docusign_logger - ERROR - Traceback:
2025-04-28 16:30:20,012 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-04-28 16:30:20,012 - docusign_logger - ERROR - ==================================================
2025-04-28 16:30:20,012 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
