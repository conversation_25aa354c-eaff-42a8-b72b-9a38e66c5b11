import sys
from shutil import copyfile
from tkinter import *
from dbmodels import get_context_viewpoint_field_tickets, get_pm_subcontract_pmsl_table, replace_none_with_empty_str, create_scope_list, download_template
from PyPDF2 import PdfFileMerger
import os
from datetime import datetime
from docx2pdf import convert
from tkinter import messagebox
from attachment_utils import GetAttachmentsFromBlob
# from eSignatureapi_docusign_viewpoint_integration import check_template_name, download_template_azure_blob
#from template_management import check_template_name, download_template_azure_blob
from config import *
from doc_text_replacement import doc_replace_key_values,create_exhibits_from_docs,merge_documents
from mailmerge import MailMerge
from time import time
from pathlib import Path, PurePath
from shutil import copyfile
import inflect
from docx import Document as Document_
from docx.oxml.ns import qn
from docx.oxml import OxmlElement

def update_status_panel(status_text, master, text):
    status_text.insert(END, "\r\n" + text)
    master.update()

def convert_table_rows_to_string(table_data_rows):
    table_data = []
    for row in table_data_rows:
        dictionary_row = replace_none_with_empty_str(row)
        for key, value in dictionary_row.items():     #Converting all the values in the dictionary to string. This is required for the MailMerge library to work
            dictionary_row[key] = str(value).strip()
        table_data.append(dictionary_row)
    return table_data

def manage_third_signer_field(total_signers, final_docx):
    if total_signers == 2:    #Its a 2 signer case. So delete the table for Manager Signature
        fd = Document_(final_docx)
        allTables = fd.tables
        for activeTable in allTables:
            try:
                if 'Customer 2 Signature' in activeTable.cell(0, 0).paragraphs[1].text:
                    activeTable._element.getparent().remove(activeTable._element)
            except:
                pass
        fd.save(final_docx)

def manage_stamp_field_textfields(final_docx, use_stamp_by_subcontractor):
    if use_stamp_by_subcontractor != 'Y':
        print('We need to remove the stamps table for internal')
        fd = Document_(final_docx)
        allTables = fd.tables
        for activeTable in allTables:
            try:
                print('I got table', activeTable.cell(0, 0).paragraphs[0].text)
                if 'Customer Stamp' in activeTable.cell(0, 0).paragraphs[0].text:
                    activeTable._element.getparent().remove(activeTable._element)
            except:
                print('Exception occured while deleting the Customer Stamp table')
                pass
        fd.save(final_docx)

from docx import Document as Document_

def remove_data_tables(final_docx, table_name, table_rows):
    fd = Document_(final_docx)
    allTables = fd.tables
    for activeTable in allTables:
        try:
            # Check in the first row, first cell
            cell_text_row1 = activeTable.cell(0, 0).paragraphs[0].text
            print('Found in Row 1: ', cell_text_row1)
            if table_name in cell_text_row1 and table_rows == 0:
                activeTable._element.getparent().remove(activeTable._element)
                print('Deleted ', table_name)
                continue

            # Check in the second row, first cell
            cell_text_row2 = activeTable.cell(1, 0).paragraphs[0].text
            print('Found in Row 2: ', cell_text_row2)
            if table_name in cell_text_row2 and table_rows == 0:
                activeTable._element.getparent().remove(activeTable._element)
                print('Deleted ', table_name)

        except Exception as e:
            print('Exception occurred: ', e)
            pass

    fd.save(final_docx)
    # f = Path(final_docx)
    # saveas = f.stem + '_v1' + Path(final_docx).suffix
    # head, tail = os.path.split(final_docx)
    # fd.save(os.path.join(head, saveas))
    # return saveas

def find_and_remove_text(doc, text):
    for paragraph in doc.paragraphs:
        if text in paragraph.text:
            # Remove the marker text and return the paragraph
            paragraph.text = paragraph.text.replace(text, "")
            return paragraph
    return None

def copy_table_after(table, paragraph, doc):
    # Copy the table after the specified paragraph
    tbl, p = table._tbl, paragraph._p
    new_tbl = OxmlElement('w:tbl')
    new_tbl.append(tbl)
    p.addnext(new_tbl)
    return new_tbl

def move_table_after(tbl_element, ref_paragraph):
    ref_p = ref_paragraph._p
    ref_p.addnext(tbl_element)

def merge_documents_with_tables(parent_doc_path, child_docs_paths, marker_text):
    # Load the parent document
    parent_doc = Document_(parent_doc_path)

    # Find the paragraph with the marker text and remove it
    marker_paragraph = find_and_remove_text(parent_doc, marker_text)
    if not marker_paragraph:
        raise ValueError(f"Marker text '{marker_text}' not found in the document.")

    # List to track the newly added tables
    new_tables = []

    # Process each child document
    for child_path in child_docs_paths:
        # Load the child document
        child_doc = Document_(child_path)

        # Extract and copy each table from the child document
        for child_table in child_doc.tables:
            new_tbl_element = copy_table_after(child_table, parent_doc.paragraphs[-1], parent_doc)
            new_tables.append(new_tbl_element)
            #parent_doc = add_line_break(parent_doc)

    # Move all new tables to the position of the marker paragraph
    for tbl_element in reversed(new_tables):
        move_table_after(tbl_element, marker_paragraph)

    # Save the combined document
    base_dir = os.path.dirname(os.path.realpath(__file__))
    os.chdir(base_dir)
    combined_doc_path = os.path.join(base_dir, TEMPLATES_PATH, 'combined_document.docx')
    parent_doc.save(combined_doc_path)

    return combined_doc_path


def format_address(address):
    words = address.split()
    formatted_words = []
    
    # List of directional suffixes that should remain uppercase
    directionals = ["NE", "NW", "SE", "SW", "N", "S", "E", "W"]
    
    for word in words:
        if word.upper() in directionals:
            formatted_words.append(word.upper())
        else:
            formatted_words.append(word[0].upper() + word[1:].lower())
    
    return ' '.join(formatted_words)

def build_documents(company, workorder, scope, scope_begin, scope_end, show_work_date_range, template_filename, template_fullpath, attachment_list, status_text, master, ticketid):
    #try:
    bgn_tm = time()
    print('Inside build_documents with these parameters: ', company, workorder, scope, scope_begin, scope_end, template_filename, template_fullpath, attachment_list)
    base_dir = os.path.dirname(os.path.realpath(__file__))
    os.chdir(base_dir)

    scope_list = create_scope_list(company, workorder,scope)
    print('Data will be prepared for these scopes: ', scope_list)

    date_time_str = datetime.now().strftime('%Y%m%d%H%M%S')
    generated_docx_filename = template_filename.split('.')[0] + '_' + date_time_str + '.' + template_filename.split('.')[1]
    generated_pdf_filename = template_filename.split('.')[0] + '_' + date_time_str + '.pdf'

    
    total_amount = 0
    child_templates = []
    context_row_common_data = None
    for scope_value in scope_list:
        df_list = get_context_viewpoint_field_tickets(template_filename, company, workorder, int(scope_value), scope_begin, scope_end)
        context_rows = df_list[0].to_dict('records')
        
        #Saving the context rows for using after the for loop. 
        if context_rows is not None:
            if len(context_rows) > 0:
                context_row_common_data = context_rows[0]
        #     temp_context_rows = df_list[0].to_dict('records')
        #     if temp_context_rows:  # Ensure the data is not empty
        #         context_rows = temp_context_rows

        equipment_rows = df_list[1].to_dict('records')
        labour_rows = df_list[2].to_dict('records')
        misc_rows = df_list[3].to_dict('records')
        #print('LABOUR ', labour_rows)

        total_amount = total_amount + df_list[1]['EquipmentPriceTotal'].sum() + df_list[2]['TechnicianPriceTotal'].sum() + df_list[3]['MiscPriceTotal'].sum()
        print(f'$$$ upto scope {int(scope_value)}, total amount is = {total_amount}')

        if context_rows is None:    #No data found. So go to next scope
            continue

        equipment_table_data = convert_table_rows_to_string(equipment_rows)
        equipment_total = 0
        for item in equipment_table_data:
            equipment_total += float(item['EquipmentPriceTotal'])
            item['EquipmentBillableRate'] = '${:,.2f}'.format(float(item['EquipmentBillableRate']))
            
            units_value = float(item['EquipmentBillableTimeUnits'])
            if units_value < 0:
                item['EquipmentBillableTimeUnits'] = '(${:,.2f})'.format(abs(units_value))
            else:
                item['EquipmentBillableTimeUnits'] = '{:,.2f}'.format(units_value)

            total_value = float(item['EquipmentPriceTotal'])
            if total_value < 0:
                item['EquipmentPriceTotal'] = '(${:,.2f})'.format(abs(total_value))
            else:
                item['EquipmentPriceTotal'] = '{:,.2f}'.format(total_value)
            

        labour_table_data = convert_table_rows_to_string(labour_rows)
        labour_total = 0
        for item in labour_table_data:
            labour_total += float(item['TechnicianPriceTotal'])
            item['TechnicianPayRate'] = '${:,.2f}'.format(float(item['TechnicianPayRate']))

            units_value = float(item['TechnicianPayQuantity'])
            if units_value < 0:
                item['TechnicianPayQuantity'] = '(${:,.2f})'.format(abs(units_value))
            else:
                item['TechnicianPayQuantity'] = '{:,.2f}'.format(units_value)

            total_value = float(item['TechnicianPriceTotal'])
            if total_value < 0:
                item['TechnicianPriceTotal'] = '(${:,.2f})'.format(abs(total_value))
            else:
                item['TechnicianPriceTotal'] = '{:,.2f}'.format(total_value)
            
        

        misc_table_data = convert_table_rows_to_string(misc_rows)
        misc_total = 0
        for item in misc_table_data:
            misc_total += float(item['MiscPriceTotal'])
            item['MiscPriceRate'] = '${:,.2f}'.format(float(item['MiscPriceRate']))

            units_value = float(item['MiscPriceQuantity'])
            if units_value < 0:
                item['MiscPriceQuantity'] = '(${:,.2f})'.format(abs(units_value))
            else:
                item['MiscPriceQuantity'] = '{:,.2f}'.format(units_value)

            total_value = float(item['MiscPriceTotal'])
            if total_value < 0:
                item['MiscPriceTotal'] = '(${:,.2f})'.format(abs(total_value))
            else:
                item['MiscPriceTotal'] = '{:,.2f}'.format(total_value)
            

        ##Setting labour name First Name space Last Name
        for item in labour_table_data:
            temp = item['EmployeeName']
            newname = str(temp.split(',')[1]).strip() + ' ' + str(temp.split(',')[0]).strip()
            item['EmployeeName'] = newname

        update_status_panel(status_text, master, "***Data from Vista has been retrieved for the template.")

        if len(equipment_table_data) == 0 and len(labour_table_data) == 0 and len(misc_table_data) == 0:
            continue
        
        ##Download the labor-equipment-misc table template
        if int(company) in EHSP_COMPANIES:
            selected_field_ticket_tables = WORK_COMPLETED_TABLES_EHSP
        else:
            selected_field_ticket_tables = WORK_COMPLETED_TABLES

        download_template(CLIENT_ID, os.path.join(base_dir, TEMPLATES_PATH, selected_field_ticket_tables))


        ##Show the tables if there is data only. Otherwise delete the table fromt the template
        if len(equipment_table_data) == 0:
            remove_data_tables(os.path.join(base_dir, TEMPLATES_PATH, selected_field_ticket_tables), 'Equipment', len(equipment_table_data))

        if len(labour_table_data) == 0:
            remove_data_tables(os.path.join(base_dir, TEMPLATES_PATH, selected_field_ticket_tables), 'Labor', len(labour_table_data))

        if len(misc_table_data) == 0:
            remove_data_tables(os.path.join(base_dir, TEMPLATES_PATH, selected_field_ticket_tables), 'Misc', len(misc_table_data))

        ##MailMerge library document building started
        print('MailMerge library document building started')
        bgn_tm = time()
        document_1 = MailMerge(os.path.join(base_dir, TEMPLATES_PATH, selected_field_ticket_tables))

        context_data = replace_none_with_empty_str(context_rows[0])
        for key, value in context_data.items():     #Converting all the values in the dictionary to string. This is required for the MailMerge library to work
            context_data[key] = str(value).strip()
        #context_data['MailAddress'] = format_address(context_data['MailAddress'])   #Converting address like "3602 BLACKFOOT TRAIL SE" to "3602 Blackfoot Trail SE"
        document_1.merge(**context_data)

        if len(equipment_table_data) > 0:
            document_1.merge_rows('EquipmentName', equipment_table_data)

        if len(labour_table_data) > 0:
            document_1.merge_rows('EmployeeName', labour_table_data)

        if len(misc_table_data) > 0:
            document_1.merge_rows('MiscDescription', misc_table_data)

        context_total = {}

        total_value = labour_total + equipment_total + misc_total
        if total_value < 0:
            context_total['ScopeSubTotal'] = '(${:,.2f})'.format(abs(total_value))
        else:
            context_total['ScopeSubTotal'] = '{:,.2f}'.format(total_value)

        if labour_total < 0:
            context_total['LaborTotal'] = '(${:,.2f})'.format(abs(labour_total))
        else:
            context_total['LaborTotal'] = '${:,.2f}'.format(labour_total)

        if equipment_total < 0:
            context_total['EquipmentTotal'] = '(${:,.2f})'.format(abs(equipment_total)) 
        else:
            context_total['EquipmentTotal'] = '${:,.2f}'.format(equipment_total)

        if misc_total < 0:
            context_total['MiscTotal'] = '(${:,.2f})'.format(abs(misc_total))
        else:
            context_total['MiscTotal'] = '${:,.2f}'.format(misc_total)

        document_1.merge(**context_total)

        saveas = os.path.join(base_dir, TEMPLATES_PATH, selected_field_ticket_tables.split('.')[0] + '_' + str(scope_value) + '.docx')
        print('Staring mail merge to the child template', saveas)
        document_1.write(saveas)
        print('****Total time needed to build the doc using MailMerge library: ', time() - bgn_tm, '\r\n\r\n')
        update_status_panel(status_text, master, "***Mailmerge template has been generated successfully for " + saveas + ". Total time needed = " + str(round(time() - bgn_tm,2)) + " seconds.")
        child_templates.append(saveas)


    parent_doc_path = template_fullpath
    child_docs_paths = child_templates

    # Marker text to identify where to insert the tables
    marker_text = "XXYY"

    # Merge the documents
    combined_doc_path = merge_documents_with_tables(parent_doc_path, child_docs_paths, marker_text)

    print(f"Finally the combined document is created: {combined_doc_path}")

    print(context_row_common_data)
    #2.Fixing the resulted dictionary. There are 2 dictionaries here: 1 for the context data and another for the table data
    context_data = replace_none_with_empty_str(context_row_common_data)
    for key, value in context_data.items():     #Converting all the values in the dictionary to string. This is required for the MailMerge library to work
        context_data[key] = str(value).strip()
    context_data['TotalAmount'] = '${:,.2f}'.format(total_amount) if total_amount > 0 else '(${:,.2f})'.format(abs(total_amount))

    #context_data['WorkOrderID'] = str(context_data['WorkOrderID']) + '-' + get_next_workorder_sequence(int(context_data['SMCompanyID']), int(context_data['WorkOrderID']))
    ## As per change request, I am adding this new method to add ticket number:
    context_data['WorkOrderID'] = str(context_data['WorkOrderID']) + '-' + str(ticketid).zfill(3)

    if show_work_date_range:    ##This is a new change request. If both the scope start and scope end dates are provided, then show the date range in work date position, otherwise show single scope end date
        context_data['RequestedDateFormatted'] = datetime.strptime(str(scope_begin).split(' ')[0], "%Y-%m-%d").strftime("%m/%d/%Y") + ' - ' + datetime.strptime(str(scope_end).split(' ')[0], "%Y-%m-%d").strftime("%m/%d/%Y")  
    else:
        context_data['RequestedDateFormatted'] = datetime.strptime(str(scope_end).split(' ')[0], "%Y-%m-%d").strftime("%m/%d/%Y")


    #3. Using MailMerge library to populate the template with data from Vista
    saveas = os.path.join(base_dir, DOCUSIGN_Ready_Path, generated_docx_filename)

    #print("Fields included in the document", document_1.get_merge_fields())
    print(saveas)
    # Merge data and the tables
    document_1 = MailMerge(combined_doc_path)
    document_1.merge(**context_data)
    document_1.write(saveas)
    print('****Total time needed to build the doc using MailMerge library: ', time() - bgn_tm, '\r\n\r\n')
    ##Add rows to the table in the word template if there is data only
    

    

    #4. Converting the docx into pdf. No attachment for Bravo. Adding ticket number into the filename
    final_merged_pdf = generated_pdf_filename.split('.')[0] + '_' + str(context_data['WorkOrderID']) + '_unsigned.' + generated_pdf_filename.split('.')[1]
    
    # update_status_panel(status_text, master, "***Final pdf is ready for signature. Total time needed = " + str(round(time() - bgn_tm,2)) + " seconds.")
    # update_status_panel(status_text, master, "***Total Time needed for the whole process = " + str(round(time() - process_start_time,2)) + " seconds.")
    return saveas, final_merged_pdf, context_data['WorkOrderID'], context_data['RequestedDateFormatted']

    # except Exception as e:
    #     print('Exception occured: ', e)
    #     return None, None, None, None