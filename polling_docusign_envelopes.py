import requests
from dbmodels import get_unsigned_documents, update_signing_info, insert_attachment_vpattachment_database_azure
from jose import jws
from cryptography.hazmat.primitives import serialization as crypto_serialization
import os
from docusign_esign import RecipientViewRequest, EnvelopeDefinition, Document, Signer, SignHere, Approve, Tabs, Recipients, ApiClient, EnvelopesApi, Text as CustomField, DateSigned, CarbonCopy, InitialHere, RecipientEmailNotification
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend
import json
import jwt
from datetime import date
from docusign import create_api_client
import shutil
from datetime import date
from datetime import datetime
import time
from datetime import timedelta
from tkinter import *
from tkinter import messagebox
from tkinter.ttk import *
#from pdf_modification import hide_signature_surroundings, redact
from config import *
from utils import *
from docusign_bulksend import DocuSignBulkSender
from exception_logger import *
logger = setup_logger()
logger.info("Polling DocuSign Envelopes: started")


log = ''

def initialize_config(secret_data):
    # Database URLs
    Config.CONN_STR_VIEWPOINT = secret_data.get('VIEWPOINT_DB_URL')
    Config.CONN_STR_VPATTACHMENT = secret_data.get('VP_ATTACHMENTS_DB_URL')
    Config.CONN_STR_SMART_SIGN = secret_data.get('ESIGNATURE_DB_URL')
    
    # DocuSign configuration
    Config.DS_INTEGRATION_KEY = secret_data.get('DS_INTEGRATION_KEY')
    Config.DS_ACCOUNT_ID = secret_data.get('DS_ACCOUNT_ID')
    Config.DS_USER_ID = secret_data.get('DS_USER_ID')
    Config.DS_PRIVATE_KEY_FILE = secret_data.get('DS_PRIVATE_KEY_FILE').encode('utf-8')
    Config.DS_AUTHORIZATION_SERVER = secret_data.get('DS_AUTHORIZATION_SERVER')
    Config.DS_BASE_URL = secret_data.get('DS_BASE_URL')
    Config.DS_ACCOUNT_URL = secret_data.get('DS_ACCOUNT_URL')
    Config.DS_BRAND_ID = secret_data.get('DS_BRAND_ID')
    Config.DS_BASE_PATH = secret_data.get('DS_BASE_PATH') + '/restapi'


def docusign_token():
    iat = int(time.time())  # Convert to integer
    exp = iat + (3600 * 24)  # 24 hours token validity

    payload = {
        "sub": Config.DS_USER_ID,
        "iss": Config.DS_INTEGRATION_KEY,
        "iat": iat,
        "exp": exp,
        "aud": Config.DS_AUTHORIZATION_SERVER,
        "scope": "signature"
    }

     # No need to encode, Config.DS_PRIVATE_KEY_FILE is already bytes
    private_key_bytes = Config.DS_PRIVATE_KEY_FILE

    # Load the private key
    private_key = serialization.load_pem_private_key(
        private_key_bytes,
        password=None
    )

    # Convert private key to PEM format
    key = private_key.private_bytes(
        serialization.Encoding.PEM,
        serialization.PrivateFormat.PKCS8,
        serialization.NoEncryption()
    )

    # Generate JWT using PyJWT (correct way)
    jwt_token = jwt.encode(payload, key, algorithm="RS256")

    return jwt_token

def create_jwt_grant_token():
    token = docusign_token()
    return token

def initialize_docusign_api():
    #getting docusign token, creating api client
    token = create_jwt_grant_token()
    post_data = {'grant_type': 'urn:ietf:params:oauth:grant-type:jwt-bearer', 'assertion': token} 
    base_url = 'https://' + Config.DS_AUTHORIZATION_SERVER + '/oauth/token'
    r = requests.post(base_url, data=post_data)
    token = r.json()
    api_client = ApiClient()
    api_client.host = Config.DS_BASE_PATH
    api_client.set_default_header('Authorization', 'Bearer ' + token["access_token"])
    envelopes_api = EnvelopesApi(api_client)
    return envelopes_api

def check_docusign_signed_doc(envelopes_api):
    def start_polling_within_tkinter():
        try:
            base_dir = os.path.dirname(os.path.realpath(__file__))
            os.chdir(base_dir)
            parent_dir = os.path.abspath(os.path.join(base_dir, os.pardir))
            master.iconbitmap(os.path.join(base_dir, "icons", 'logo.ico'))
            
            args = parse_args()
            username =  str(args['Username']).strip()

            pb1['value'] = 10
            master.update_idletasks()

            distinct_envelopes = []
            envelope_dict = {}
            batch_envelope_map = {}
            docs = get_unsigned_documents()
            if len(docs) == 0:
                #print(f'No record found in last {Config.POLLING_PERIOD} days.')
                logger.info(f"No record found in last {Config.POLLING_PERIOD} days.")
                pb1['value'] = 100
                master.update_idletasks()
                messagebox.showinfo(f"Vista by Viewpoint",  f"No batch found in last {Config.POLLING_PERIOD} days.")
                master.destroy()
                return None
            else:
                for row in docs:
                    batch_id = row['BatchID']
                    envelope_id = row['EnvelopeId']
                    employee = row['Employee']
                    fullname = row['RecipientName']
                    company = row['Co']
                    job = row['Job']
                    has_signed = row['HasSigned']
                    
                    if batch_id not in batch_envelope_map:
                        batch_envelope_map[batch_id] = []
                    
                    # Add the envelope_id to the list for this batch
                    batch_envelope_map[batch_id].append({
                        'envelope_id': envelope_id,
                        'employee': employee,
                        'fullname': fullname,
                        'company': company,
                        'job': job,
                        'filename': str(employee) + ' - ' + str(fullname) + '.pdf',
                        'HasSigned': has_signed
                    })
                    distinct_envelopes.append(envelope_id)

                    
                distinct_envelopes = list(set(distinct_envelopes))
                #print('Total ', len(distinct_envelopes), 'docusign envelopes have been found in custom form [dbo].[budDSBulkBatchTrack] in Vista.')
                logger.info(f"Total {len(distinct_envelopes)} docusign envelopes have been found in custom form [dbo].[budDSBulkBatchTrack] in Vista.")

            #setting base directory
            base_dir = os.path.dirname(os.path.realpath(__file__))
            os.chdir(base_dir)


            pb1['value'] = 20
            master.update_idletasks()

            for batch_id, employee_list in batch_envelope_map.items():
                #print(f"Working for Batch ID: {batch_id},\n Employee Info: {employee_list}")
                logger.info(f"Working for Batch ID: {batch_id},\n Employee Info: {employee_list}")

                try:
                    # Get access token from the envelopes_api that was passed to the function
                    #access_token = envelopes_api.api_client.default_headers["Authorization"].split("Bearer ")[1]
                    for employee_info in employee_list:
                        envelope_id = employee_info['envelope_id']
                        employee = employee_info['employee']
                        fullname = employee_info['fullname']
                        filename = employee_info['filename']
                        has_signed = employee_info['HasSigned']
                        company = employee_info['company']
                        job = employee_info['job']
                        
                        # Get envelope status using the EnvelopesApi
                        envelope_details = envelopes_api.get_envelope(account_id=Config.DS_ACCOUNT_ID,envelope_id=envelope_id)
                        
                        # Check envelope status
                        if envelope_details.status == 'completed' and has_signed == 'Y':
                            #print(f"Envelope ID: {envelope_id}, with status: {envelope_details.status} has already been signed.")
                            logger.info(f"Envelope ID: {envelope_id}, with status: {envelope_details.status} has already been signed.")

                        elif envelope_details.status == 'completed' and has_signed == 'N':
                            document_id = "1"
                            temp_file = envelopes_api.get_document(
                                account_id=Config.DS_ACCOUNT_ID,
                                envelope_id=envelope_id,
                                document_id=document_id
                            )
                            
                            # Now move the file into an archived location and rename it with datetime
                            date_time_str = datetime.now().strftime('%Y%m%d%H%M%S')
                            date_time_str2 = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            generated_pdf_filename = filename
                            
                            # Create destination path
                            completed_path = os.path.join(base_dir, Config.DOCUSIGN_CompletedPath)
                            destination_file = os.path.join(completed_path, generated_pdf_filename)

                            if os.path.exists(destination_file):
                                os.remove(destination_file)
                            
                            # Move the file
                            shutil.move(temp_file, destination_file)

                            # Update database
                            update_signing_info(date_time_str2, envelope_id)
                            
                            # Insert into attachment database
                            insert_attachment_vpattachment_database_azure(destination_file, generated_pdf_filename, company, job, username)
                            
                            print('Processed envelope ID: ', envelope_id, 'for employee: ', employee, ', Name: ', fullname, ',Employee ID: ', employee)

                        else:
                            #print(f"Envelope ID: {envelope_id}, with status: {envelope_details.status}.")
                            logger.info(f"Envelope ID: {envelope_id}, with status: {envelope_details.status}.")

                        pb1['value'] += 5
                        master.update_idletasks()
                
                except Exception as e:
                    print(f"Error processing batch {batch_id}: {str(e)}")


            pb1['value'] = 100
            master.update_idletasks()
            messagebox.showinfo("Vista by Viewpoint",  "Syncing completed successfully.")
            master.destroy()
        
        except Exception as e:
            log_exception(logger, e, "Polling DocuSign Envelopes")
            error_details = traceback.format_exc()
            logger.error(f"Exception occurred: {e}, {error_details}")
            #print("Exception occurred:", error_details)
        
            exc_type, exc_obj, exc_tb = sys.exc_info()
            fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            #print(exc_type, fname, exc_tb.tb_lineno)
            logger.info(f"Exception occurred: {exc_type}, {fname}, {exc_tb.tb_lineno}")   
            messagebox.showinfo("Vista by Viewpoint",  "DocuSign exception occured. Details: " + str(exc_type) + "\r\n" + str(fname) + "\r\n" + str(exc_tb.tb_lineno) + '\r\n' + str(e))
            master.destroy()
            return


    master = Tk()
    # Set the width and height of our root window.
    master_window_height = 800
    master.geometry("900x200")
    master.title('Vista by Viewpoint: Syncing documents into viewpoint')

    pb1 = Progressbar(master, orient=HORIZONTAL, length=100, mode='determinate')
    pb1.pack(pady = 10)


    # Create label
    l = Label(master, text = "Syncing in progress. Please wait.")
    l.config(font =("Arial", 14))
    l.pack()

    master.after(2, start_polling_within_tkinter)
    master.mainloop()

if __name__ == '__main__':
    secret_data = extract_secrets_from_azure_key_vault(Config.AZURE_KEYVAULT_SECRET_VALUE)
    initialize_config(secret_data)
    envelopes_api = initialize_docusign_api()
    check_docusign_signed_doc(envelopes_api)
