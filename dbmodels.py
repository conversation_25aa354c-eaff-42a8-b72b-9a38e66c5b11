import pyodbc
import sys
from config import *
import os
from datetime import datetime


def dictfetchall(cur):
    dataset = cur.fetchall()
    columns = [col[0] for col in cur.description]
    return [
        dict(zip(columns, row))
        for row in dataset
        ]


def get_vendor_details(vendor_group, vendor):
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    query = """select * from dbo.APVM
                WHERE VendorGroup = """ + str(vendor_group) + """
                AND Vendor = """ + str(vendor)
    cursor.execute(query)
    data = dictfetchall(cursor)
    cursor.close()
    conn.close()
    return data    

def get_crystal_report_config(report_id, client_id):
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    query = f"""select * from [dbo].[z_OC_CrystalReportConfig] where ClientId = {client_id} and LEFT(ReportId, 30) = '{report_id}'"""
    cursor.execute(query)
    data = dictfetchall(cursor)
    cursor.close()
    conn.close()
    return data    



def get_lookup_columns(lookup):
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    query = f"""select * from [dbo].[DDLDShared]
                where Lookup = '{lookup}' order by Seq"""
    cursor.execute(query)
    data = dictfetchall(cursor)
    cursor.close()
    conn.close()
    return data    

def execute_sql_statement(sql_statement):
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    try:
        cursor.execute(sql_statement)
        conn.commit()  # Commit the transaction
        print("SQL statement executed successfully.")
    except pyodbc.Error as e:
        conn.rollback()  # Rollback in case of error
        print(f"An error occurred: {str(e)}")
    finally:
        cursor.close()
        conn.close()

def get_report_parameters(report_id):
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    query = f"""select * from dbo.RPRPShared
                where ReportID = {report_id}"""
    cursor.execute(query)
    data = dictfetchall(cursor)
    cursor.close()
    conn.close()
    return data   


def get_PREH_job_details(company, job, beginning_employee=None, ending_employee=None, employee_alternate_email=None):
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()

    if beginning_employee and ending_employee:
        employee_where_clause = f'AND "bPREH"."Employee" >= {int(beginning_employee)} AND "bPREH"."Employee" <= {int(ending_employee)}'
    else:
        employee_where_clause = 'AND ("bPREH"."Employee" >= 0 AND "bPREH"."Employee" <= *********)'

    if employee_alternate_email:
        breh_email = f"'{employee_alternate_email}' as Email"
    else:
        breh_email = '"bPREH"."Email" as Email'

    query = f"""SELECT DISTINCT
                "bPREH"."Employee", 
                "bPREH"."LastName", 
                "bPREH"."ActiveYN", 
                "bPREH"."PRCo", 
                "bPREH"."Suffix", 
                "bPREH"."FirstName", 
                "bHQCO"."Name", 
                "bHQCO"."Address", 
                "bHQCO"."City", 
                "bHQCO"."State", 
                "bHQCO"."Zip", 
                "bHQCO"."Phone", 
                {breh_email}, 
                "bPREH"."PRGroup", 
                "bPREH"."CellPhone",
                "bJCJM"."Job",
                "bJCJM".udJobAwardDate,
                "bJCJM".udStartDate,
                "bJCJM".udMeetingDate,
                "bJCJM".udGroupOrIndividuals,
                "bJCJM".udRepresentativeName,
                "bJCJM".udRepresentativeEmail,
                "bJCJM".udBeginningEmployee,
                "bJCJM".udEndingEmployee,
                "bJCJM".udEmployeeAlternativeEmail,
                "bJCJM"."Description",
                "bJCJM"."Certified"
            FROM
                "dbo"."bJCJM" "bJCJM" 
                LEFT OUTER JOIN "dbo"."bPRTC" "bPRTC" 
                    ON ("bJCJM"."JCCo" = "bPRTC"."PRCo") 
                    AND ("bJCJM"."CraftTemplate" = "bPRTC"."Template")
                LEFT OUTER JOIN "dbo"."bHQCO" "bHQCO" 
                    ON "bJCJM"."JCCo" = "bHQCO"."HQCo"
                LEFT OUTER JOIN "dbo"."bPREH" "bPREH" 
                    ON "bJCJM"."JCCo" = "bPREH"."PRCo"
                LEFT OUTER JOIN "dbo"."bPRTP" "bPRTP" 
                    ON ("bPRTC"."PRCo" = "bPRTP"."PRCo") 
                    AND ("bPRTC"."Craft" = "bPRTP"."Craft") 
                    AND ("bPRTC"."Class" = "bPRTP"."Class") 
                    AND ("bPRTC"."Template" = "bPRTP"."Template")
                LEFT OUTER JOIN [dbo].[budDSBulkBatchTrack] "budDSBulkBatchTrack"
                    ON "bPREH"."Employee" = "budDSBulkBatchTrack"."Employee"
                    AND "bPREH"."PRCo" = "budDSBulkBatchTrack"."PRCo"
                    AND "bPREH"."PRGroup" = "budDSBulkBatchTrack"."PRGroup"
                    AND "bJCJM"."Job" = "budDSBulkBatchTrack"."Job"
            WHERE
                "bPRTP"."Shift" = {Config.CRYSTAL_REPORT_EAC_MW212_QUERY_PARAM_Shift}
                AND "bPREH"."PRGroup" = {Config.CRYSTAL_REPORT_EAC_MW212_QUERY_PARAM_PRGroup}
                AND "bPREH"."PRCo" = {company}
                AND "bPREH"."ActiveYN" = 'Y' 
                {employee_where_clause}
                AND "bJCJM"."Certified" = 'Y' 
                AND "bJCJM"."Job" = '{job}' 
                AND (
                    "budDSBulkBatchTrack"."Employee" IS NULL  -- Employees not in the batch track
                    OR "budDSBulkBatchTrack"."Resend" = 'Y'  -- OR employees marked for resend
                )
            ORDER BY
                "bJCJM"."Job", 
                "bPREH"."LastName",
                "bPREH"."FirstName"
    """
    # query = f"""SELECT b.* from dbo.z_OC_Temp_Bulk_Send_Employee as b
    #             LEFT OUTER JOIN [dbo].[budDSBulkBatchTrack] "budDSBulkBatchTrack"
    #                     ON  b."Employee" = "budDSBulkBatchTrack"."Employee"
    #                     AND "b"."PRCo" = "budDSBulkBatchTrack"."PRCo"
    #                     AND "b"."PRGroup" = "budDSBulkBatchTrack"."PRGroup"
    #                     AND "b"."Job" = "budDSBulkBatchTrack"."Job"
    #             WHERE
    #             (
    #                 "budDSBulkBatchTrack"."Employee" IS NULL  -- Employees not in the batch track
    #                 OR "budDSBulkBatchTrack"."Resend" = 'Y'  -- OR employees marked for resend
    #             )
    # """       ##Remove this query to get all employees
    cursor.execute(query)
    data = dictfetchall(cursor)
    cursor.close()
    conn.close()
    return data


def insert_attachment_vpattachment_database_azure(filepath, final_merged_pdf, company, project, added_by):
    date_time_str2 = datetime.now().strftime('%Y-%m-%d %H:%M')
    file_description = 'Signed Copy ' + date_time_str2
    insert_docusign_pdf_blob_vpattachment_azure(filepath, final_merged_pdf, file_description, str(company), str(project), added_by)


def insert_docusign_pdf_blob_vpattachment_azure(filepath, filename, description, company, project, added_by):
    ##Step 1: prepare related variables in viewpoint
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    qry = """DECLARE @AttachID int,
                @KeyField varchar(30) = CONCAT('KeyID=',(SELECT KeyID FROM JCJM WHERE JCCo = """ + str(company) + """ AND LTRIM(RTRIM(Job)) = '""" + str(project).strip() + """')),
                @AddedBy varchar(100) = '""" + added_by + """',
                @AddDate bDate = GETDATE()
                EXEC vspHQATInsert """ + str(company) + """,'JCJM', @KeyField, '""" + description + """', @AddedBy, @AddDate, 'Database', 'JCJM', '""" + filename + """', @AttachID, null, 'N', 'N', 0, 'N', null"""
    #print(qry)
    cursor.execute(qry)
    cursor.close()
    conn.commit()
    conn.close()

    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    query = """SELECT MAX(AttachmentID) as MaxId FROM HQAT"""
    cursor.execute(query)
    data = dictfetchall(cursor)
    cursor.close()
    conn.close()
    maxid = data[0]['MaxId']

    ##Step 5: Insert this document into VPAttachment database directly.
    with open(filepath, mode='rb') as file: # b is important -> binary
        fileContent = file.read()

    conn = pyodbc.connect(Config.CONN_STR_VPATTACHMENT)
    cursor = conn.cursor()
    sql = "insert HQAF(AttachmentID, AttachmentFileType, AttachmentData) values (?,?,?)"
    cursor.execute(sql, (maxid, 'pdf', (fileContent)))
    cursor.close()
    conn.commit()
    conn.close()

def get_template_details(template_name):
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    query = """Select *
            FROM [dbo].[z_OC_TemplateDetails]
            WHERE [Filename] = '""" + template_name + "' AND ClientId = " + str(Config.CLIENT_ID)
    cursor.execute(query)
    data = dictfetchall(cursor)
    cursor.close()
    conn.close()
    return data

def insert_signing_request(signer_name, signer_email, cc_name, cc_email, signing_order, envelope_id, has_signed, log_date, signing_date, company, project, sl_number, template_id, source):
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    sql = "insert into [dbo].[z_OC_SigningRequest]([SignerName],[SignerEmail],[CCName],[CCEmail],[SigningOrder], [EnvelopeId],[HasSigned],[LogDate],[SigningDate],  [Company],[Project],[SLNumber], [TemplateId], [Source]) values (?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?)"
    cursor.execute(sql, (signer_name, signer_email, cc_name, cc_email, signing_order, envelope_id, has_signed, log_date, signing_date, company, project, sl_number, template_id, source))
    cursor.close()
    conn.commit()
    conn.close()

def insert_into_custom_tracker(company, employee_details, batch_id, job, username):
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    
    # Get the max KeyID with null check
    cursor.execute("SELECT ISNULL(MAX(ID), 0) + 1 AS MaxID FROM [dbo].[budDSBulkBatchTrack]")
    max_id = cursor.fetchone()[0]
    
    sql = """INSERT INTO [dbo].[budDSBulkBatchTrack]
                (ID, Co, PRCo, PRGroup, Job, Employee, BatchID, EnvelopeId, HasSigned, 
                 LogDate, RecipientEmail, RecipientName, Resend, AddedBy)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"""
    
    cursor.execute(sql, (
        max_id,
        company,           # Co
        employee_details['PRCo'],              # PRCo
        employee_details['PRGroup'],           # PRGroup
        job, 
        employee_details['Employee'], 
        batch_id, 
        employee_details['envelope_id'], 
        'N',               # HasSigned - literal 'N'
        datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 
        employee_details['email'], 
        employee_details['FirstName'] + ' ' + employee_details['LastName'], 
        'N',               # Resend - literal 'N'
        username
    ))
    
    cursor.close()
    conn.commit()
    conn.close()

def get_unsigned_documents():
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    query = f"""SELECT * from [dbo].[budDSBulkBatchTrack] where HasSigned = 'N'
            order by BatchID, EnvelopeId
            """

    cursor.execute(query)
    data = dictfetchall(cursor)
    cursor.close()
    conn.close()
    return data

def update_signing_info(signing_datetime, envelope_id):
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    query = "UPDATE [dbo].[budDSBulkBatchTrack] SET [HasSigned] = 'Y', SigningDate = '" + signing_datetime + "' WHERE [EnvelopeId] = '" + str(envelope_id) + "'"
    #print(query)
    cursor.execute(query)
    cursor.close()
    conn.commit()
    conn.close()

def update_resend_checkbox():
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    query = f"UPDATE [dbo].[budDSBulkBatchTrack] SET [Resend] = 'N'"
    cursor.execute(query)
    cursor.close()
    conn.commit()
    conn.close()

def check_job_details(company, job):
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    query = f"""SELECT * from [dbo].[JCJM] where JCCo = {company} and Job = '{job}' and Certified = 'Y'"""
    cursor.execute(query)
    data = dictfetchall(cursor)
    cursor.close()
    conn.close()
    return data

def update_ud_fields(company, job, fields_to_clean):
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    query = f"UPDATE [dbo].[JCJM] SET {', '.join([f'{field} = NULL' for field in fields_to_clean])} WHERE JCCo = {company} and Job = '{job}'"
    cursor.execute(query)
    cursor.close()
    conn.commit()
    conn.close()

def check_current_version():
    conn = pyodbc.connect(Config.CONN_STR_VIEWPOINT)
    cursor = conn.cursor()
    query = """select [Version] from dbo.z_OC_Version_Control order by UpdatedOn desc"""
    cursor.execute(query)
    data = dictfetchall(cursor)
    cursor.close()
    conn.close()
    if len(data) > 0:
        return data[0]['Version']
    else:
        return None    