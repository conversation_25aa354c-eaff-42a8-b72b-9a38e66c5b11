import os
from azure.storage.blob import BlobServiceClient, BlobClient
from azure.storage.blob import ContentSettings, ContainerClient
import pyodbc
import zipfile
from tkinter import *
from tkinter import messagebox
from tkinter.ttk import *

drivers = pyodbc.drivers()
SELECTED_DRIVER = ''
if 'ODBC Driver 13 for SQL Server' in drivers:
    SELECTED_DRIVER = 'ODBC Driver 13 for SQL Server'
elif 'ODBC Driver 17 for SQL Server' in drivers:
    SELECTED_DRIVER = 'ODBC Driver 17 for SQL Server'

CONN_STR_AZURE = "Driver={" + SELECTED_DRIVER + "};Server=tcp:azurereplication.database.windows.net,1433;Database=ESignature;Uid=vistauser;Pwd=Welcome2020;Encrypt=yes;TrustServerCertificate=no;Connection Timeout=30;"
MY_CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=docusignrepository;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
MY_BLOB_CONTAINER = "earleco"     ## V.V.I **** Change the container name according to Azure

def dictfetchall(cur):
    dataset = cur.fetchall()
    columns = [col[0] for col in cur.description]
    return [
        dict(zip(columns, row))
        for row in dataset
        ]

def get_installation_path():
    conn = pyodbc.connect(CONN_STR_AZURE)
    cursor = conn.cursor()
    query = """SELECT * FROM [dbo].[Client] WHERE ClientId = 16"""       ## V.V.I **** Change client id here. Very important. 16 is for Earle Construction
    cursor.execute(query)
    data = dictfetchall(cursor)
    cursor.close()
    conn.close()
    return data[0]['InstallationPath']
 
# Replace with the local folder where you want files to be downloaded
LOCAL_BLOB_PATH = get_installation_path()
 
class AzureBlobFileDownloader:
    def __init__(self, pb1, master):
        pb1['value'] = 5
        master.update_idletasks()
        print("Intializing..")
        pb1['value'] = 10
        master.update_idletasks()
 
        # Initialize the connection to Azure storage account
        self.blob_service_client =  BlobServiceClient.from_connection_string(MY_CONNECTION_STRING)
        self.my_container = self.blob_service_client.get_container_client(MY_BLOB_CONTAINER)
        # bytes = self.my_container.get_blob_client('Penfor-Constructions').download_blob().readall()
        # self.save_blob('test123', bytes)
 
 
    def save_blob(self,file_name,file_content):
        # Get full path to the file
        download_file_path = os.path.join(LOCAL_BLOB_PATH, file_name)
 
        # for nested blobs, create local path as well!
        os.makedirs(os.path.dirname(download_file_path), exist_ok=True)
 
        with open(download_file_path, "wb") as file:
            file.write(file_content)
 
    def download_all_blobs_in_container(self, pb1, master):
        my_blobs = self.my_container.list_blobs()
        progress = 10
        pb1['value'] = progress
        master.update_idletasks()
        for blob in my_blobs:
            if 'Kernel.zip' in blob.name and os.path.exists(os.path.join(LOCAL_BLOB_PATH, 'Kernel')):
                progress += 30
                pb1['value'] = progress
                master.update_idletasks()
                print(os.path.exists(os.path.join(LOCAL_BLOB_PATH, 'Kernel')) , 'already exists.')
                continue

            print('Downloading..', blob.name)
            bytes = self.my_container.get_blob_client(blob).download_blob().readall()
            self.save_blob(blob.name, bytes)

            if 'Kernel.zip' in blob.name:
                progress += 10
                pb1['value'] = progress
                master.update_idletasks()
                print('Extracting the Kernel.zip folder..')
                with zipfile.ZipFile(os.path.join(LOCAL_BLOB_PATH, 'Kernel.zip'), 'r') as zip_ref:
                    zip_ref.extractall(LOCAL_BLOB_PATH)

                try:
                    if os.path.exists(os.path.join(LOCAL_BLOB_PATH, 'Kernel.zip')):
                        os.remove(os.path.join(LOCAL_BLOB_PATH, 'Kernel.zip'))
                except:
                    pass

                progress += 30
                pb1['value'] = progress
                master.update_idletasks()


            if 'CrystalReportManager.zip' in blob.name:
                progress += 10
                pb1['value'] = progress
                master.update_idletasks()
                print('Extracting the CrystalReportManager.zip folder..')
                
                # Delete existing CrystalReportManager directory if it exists
                import shutil
                crm_dir_path = os.path.join(LOCAL_BLOB_PATH, 'CrystalReportManager')
                if os.path.exists(crm_dir_path) and os.path.isdir(crm_dir_path):
                    print('Removing existing CrystalReportManager directory...')
                    try:
                        shutil.rmtree(crm_dir_path)
                    except Exception as e:
                        print(f"Error removing directory: {e}")
                
                with zipfile.ZipFile(os.path.join(LOCAL_BLOB_PATH, 'CrystalReportManager.zip'), 'r') as zip_ref:
                    zip_ref.extractall(LOCAL_BLOB_PATH)

                try:
                    if os.path.exists(os.path.join(LOCAL_BLOB_PATH, 'CrystalReportManager.zip')):
                        os.remove(os.path.join(LOCAL_BLOB_PATH, 'CrystalReportManager.zip'))
                except:
                    pass

                progress += 10
                pb1['value'] = progress
                master.update_idletasks()


            progress +=2
            pb1['value'] = progress
            master.update_idletasks()

        progress = 100
        pb1['value'] = progress
        master.update_idletasks()
        messagebox.showinfo("Vista by Viewpoint",  "DocuSign update has been completed successfully.")
        master.destroy()

 
def main():
    def start_docusign_download():
        azure_blob_file_downloader = AzureBlobFileDownloader(pb1, master)
        azure_blob_file_downloader.download_all_blobs_in_container(pb1, master)

    master = Tk()
    # Set the width and height of our root window.
    master_window_height = 800
    master.geometry("900x200")
    master.title('Vista by Viewpoint: Setting up DocuSign')

    pb1 = Progressbar(master, orient=HORIZONTAL, length=100, mode='determinate')
    pb1.pack(pady = 10)

    # Create label
    l = Label(master, text = "Update in progress. Please wait.")
    l.config(font =("Arial", 14))
    l.pack()

    master.after(2, start_docusign_download)
    master.mainloop()
    
if __name__ == "__main__":
    main()