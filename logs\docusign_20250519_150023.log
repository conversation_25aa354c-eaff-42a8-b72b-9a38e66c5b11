2025-05-19 15:00:23,935 - docusign_logger - INFO - DocuSign process started
2025-05-19 15:00:24,795 - docusign_logger - INFO - Docusign bulk sendprocess will be generated for below parameters: Company = 5, Job = 24140. 5, Username = olsen.sql.p4m
2025-05-19 15:00:26,312 - docusign_logger - INFO - DocuSign configuration initialized successfully from Redis or Cache
2025-05-19 15:00:29,349 - docusign_logger - INFO - Bulk send will send signature request to 76 employees.
2025-05-19 15:00:41,974 - docusign_logger - INFO - Final: Bulk send will send signature request to 1 employees: {2904}
2025-05-19 15:00:43,067 - docusign_logger - INFO - DocuSign token generated successfully: eyJ0eXAiOiJNVCIsImFsZyI6IlJTMjU2Iiwia2lkIjoiOGFlYzFjZjQtYmE4NS00MDM5LWE1MmItYzVhODAxMjA3N2EyIn0.AQoAAAABAAUABwAA-Vc3GJfdSAgAAGEcmSCX3UgCAO7hsJZI3jNJsK-gPx0JLlkVAAMAAAAYAAEAAAAFAAAADQAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4IgAkAAAAZjE2YTQ5OGQtZTU0MC00OGM0LTliYWYtMzI5ZjA2N2M5ZmI4EgABAAAABgAAAGp3dF9iciMAJAAAAGYxNmE0OThkLWU1NDAtNDhjNC05YmFmLTMyOWYwNjdjOWZiOA.BMjo9SIYfoPF_eEbxPJpanDlgw9C-c_UNdFyRt569jGpR6o_FqckG-Rf6nplAa2QrVuJ4DLTqh0iXLe32N7pz-lH_vWsW_ZExKVLvN7Fz4bWTFStVKtbNMjt75aU4Y5aUuZmMyjQiLSu5hzs5dj4wxIlf4Xwa1pzMRBXuJfe0YdzJ-7qYk-sGRcL_otclxMyARMyvxJAC1Dregzpo0F_GaA5aedCM023uQr08ACyn2fIzE0DrPY56aEETtQ6CwgH8S0UmTaktpA6ZVOFRtEwSBZNjxCkL4OuW_w-c_EK22jx_RrKNVjqcr1Dz-nj0K6jHUxJfzoo6_KSCmoPD8y4cg
2025-05-19 15:00:43,070 - docusign_logger - INFO - DocuSign instance is initialized successfully. Preparing crystal report.
2025-05-19 15:00:50,380 - docusign_logger - INFO - Crystal report generation result: {'pdf_path': '', 'doc_path': '', 'success': True, 'error': None}
2025-05-19 15:00:50,381 - docusign_logger - INFO - Crystal report generated successfully. Preparing to send documents to DocuSign.
2025-05-19 15:00:50,438 - docusign_logger - ERROR - ==================================================
2025-05-19 15:00:50,438 - docusign_logger - ERROR - EXCEPTION OCCURRED: DocuSign Main Process
2025-05-19 15:00:50,438 - docusign_logger - ERROR - Type: FileNotFoundError
2025-05-19 15:00:50,438 - docusign_logger - ERROR - Message: [Errno 2] No such file or directory: ''
2025-05-19 15:00:50,438 - docusign_logger - ERROR - Traceback:
2025-05-19 15:00:50,438 - docusign_logger - ERROR - Traceback (most recent call last):
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\PREHBulkSend\eSignatureapi_docusign_bulksend.py", line 478, in start_docusign_bulk_send_within_tkinter
    shutil.copy(pdf_filepath, pdf_destination)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 418, in copy
    copyfile(src, dst, follow_symlinks=follow_symlinks)
  File "C:\ViewpointIntegrationEarle\ViewpointDocusignBulk\Kernel\App\Python\lib\shutil.py", line 264, in copyfile
    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-05-19 15:00:50,438 - docusign_logger - ERROR - ==================================================
2025-05-19 15:00:50,439 - docusign_logger - INFO - Exception occurred: <class 'FileNotFoundError'>, eSignatureapi_docusign_bulksend.py, 478
